#!/usr/bin/env python3
import asyncio
import asyncssh
import sys
from colorama import Fore, init

# Inicializar colorama
init(autoreset=True)

# Configurações
SSH_PORT = 22
SSH_USERNAME = "root"
SSH_PASSWORD = "eE5522892004@@e"
IP_TO_CHECK = "*************"  # IP da máquina a ser verificada

async def corrigir_servidor():
    try:
        print(Fore.YELLOW + f"🔄 Conectando via SSH a {IP_TO_CHECK}...")

        # Tentar conectar via SSH
        conn = await asyncssh.connect(
            IP_TO_CHECK,
            port=SSH_PORT,
            username=SSH_USERNAME,
            password=SSH_PASSWORD,
            known_hosts=None  # Ignora verificação de known_hosts
        )

        print(Fore.GREEN + f"✅ Conexão SSH estabelecida com {IP_TO_CHECK}")

        # Verificar a linha com erro
        print(Fore.YELLOW + "🔄 Verificando a linha com erro...")
        result = await conn.run("docker exec api-enviador bash -c 'grep -n \"Rate limited by Hotmail\" /app/server.js'")
        print(Fore.CYAN + "=== Linha com erro no arquivo server.js ===")
        print(result.stdout)

        # Fazer backup do arquivo server.js
        print(Fore.YELLOW + "🔄 Fazendo backup do arquivo server.js...")
        await conn.run("docker exec api-enviador bash -c 'cp /app/server.js /app/server.js.bak'")
        print(Fore.GREEN + "✅ Backup criado em /app/server.js.bak")

        # Corrigir todas as strings de template no arquivo
        print(Fore.YELLOW + "🔄 Corrigindo todas as strings de template no arquivo...")

        # Corrigir a primeira linha com erro (Rate limited by Hotmail)
        await conn.run("docker exec api-enviador bash -c \"sed -i 's/logger.warn(\`Rate limited by Hotmail for \${to}\`);/logger.warn(\\\"Rate limited by Hotmail for \\\" + to);/g' /app/server.js\"")

        # Corrigir a segunda linha com erro (Erro ao verificar status de uploads)
        await conn.run("docker exec api-enviador bash -c \"sed -i 's/logger.error(\`Erro ao verificar status de uploads: \${error.message}\`, {/logger.error(\\\"Erro ao verificar status de uploads: \\\" + error.message, {/g' /app/server.js\"")

        # Corrigir a terceira linha com erro (Campanha não encontrada)
        await conn.run("docker exec api-enviador bash -c \"sed -i 's/logger.error(\\\"Campanha não encontrada: \${campaignId}\`);/logger.error(\\\"Campanha não encontrada: \\\" + campaignId);/g' /app/server.js\"")

        # Corrigir todas as outras strings de template que possam causar problemas
        await conn.run("docker exec api-enviador bash -c \"sed -i 's/logger.error(\`/logger.error(\\\"/g' /app/server.js\"")
        await conn.run("docker exec api-enviador bash -c \"sed -i 's/\${error.message}\`, {/\\\" + error.message, {/g' /app/server.js\"")

        # Verificar se as correções foram aplicadas
        print(Fore.YELLOW + "🔄 Verificando se as correções foram aplicadas...")
        result = await conn.run("docker exec api-enviador bash -c 'grep -n \"Rate limited by Hotmail\" /app/server.js'")
        print(Fore.CYAN + "=== Primeira linha corrigida no arquivo server.js ===")
        print(result.stdout)

        result = await conn.run("docker exec api-enviador bash -c 'grep -n \"Erro ao verificar status de uploads\" /app/server.js'")
        print(Fore.CYAN + "=== Segunda linha corrigida no arquivo server.js ===")
        print(result.stdout)

        result = await conn.run("docker exec api-enviador bash -c 'grep -n \"Campanha não encontrada\" /app/server.js'")
        print(Fore.CYAN + "=== Terceira linha corrigida no arquivo server.js ===")
        print(result.stdout)

        # Reiniciar o contêiner
        print(Fore.YELLOW + "🔄 Reiniciando o contêiner api-enviador...")
        await conn.run("docker restart api-enviador")
        print(Fore.GREEN + "✅ Contêiner reiniciado")

        # Aguardar um pouco para o serviço iniciar
        print(Fore.YELLOW + "🔄 Aguardando o serviço iniciar...")
        await asyncio.sleep(10)

        # Verificar status do contêiner
        print(Fore.YELLOW + "🔄 Verificando status do contêiner...")
        result = await conn.run("docker ps -a --filter name=api-enviador --format '{{.Names}} {{.Status}}'")
        print(Fore.CYAN + "=== Status do Contêiner api-enviador ===")
        print(result.stdout)

        # Verificar logs do contêiner
        print(Fore.YELLOW + "🔄 Verificando logs recentes do contêiner...")
        result = await conn.run("docker logs --tail 20 api-enviador")
        print(Fore.CYAN + "=== Logs Recentes do Contêiner api-enviador ===")
        print(result.stdout)

        # Tentar iniciar o servidor manualmente para ver se há outros erros
        print(Fore.YELLOW + "🔄 Tentando iniciar o servidor manualmente para ver se há outros erros...")
        result = await conn.run("docker exec api-enviador bash -c 'cd /app && node server.js' 2>&1 || echo 'Erro ao iniciar o servidor'")
        print(Fore.CYAN + "=== Resultado da Tentativa de Iniciar o Servidor ===")
        print(result.stdout)

        # Verificar se o servidor está respondendo
        print(Fore.YELLOW + "🔄 Verificando se o servidor está respondendo...")
        result = await conn.run("docker exec api-enviador curl -s http://localhost:3000/health || echo 'Servidor não está respondendo'")
        print(Fore.CYAN + "=== Resposta do Servidor ===")
        print(result.stdout)

        # Fechar conexão SSH
        conn.close()
        print(Fore.GREEN + "✅ Correção concluída")

    except Exception as e:
        print(Fore.RED + f"❌ Erro: {e}")
        return False

async def main():
    await corrigir_servidor()

if __name__ == "__main__":
    asyncio.run(main())
