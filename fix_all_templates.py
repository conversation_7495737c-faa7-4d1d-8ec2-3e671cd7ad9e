#!/usr/bin/env python3
import asyncio
import asyncssh
import sys
from colorama import Fore, init

# Inicializar colorama
init(autoreset=True)

# Configurações
SSH_PORT = 22
SSH_USERNAME = "root"
SSH_PASSWORD = "eE5522892004@@e"
IP_TO_CHECK = "*************"  # IP da máquina a ser verificada

async def corrigir_servidor():
    try:
        print(Fore.YELLOW + f"🔄 Conectando via SSH a {IP_TO_CHECK}...")
        
        # Tentar conectar via SSH
        conn = await asyncssh.connect(
            IP_TO_CHECK, 
            port=SSH_PORT, 
            username=SSH_USERNAME, 
            password=SSH_PASSWORD, 
            known_hosts=None  # Ignora verificação de known_hosts
        )
        
        print(Fore.GREEN + f"✅ Conexão SSH estabelecida com {IP_TO_CHECK}")
        
        # Fazer backup do arquivo server.js
        print(Fore.YELLOW + "🔄 Fazendo backup do arquivo server.js...")
        await conn.run("docker exec api-enviador bash -c 'cp /app/server.js /app/server.js.bak.all'")
        print(Fore.GREEN + "✅ Backup criado em /app/server.js.bak.all")
        
        # Criar um script para corrigir todos os problemas de string de template
        print(Fore.YELLOW + "🔄 Criando script para corrigir todos os problemas de string de template...")
        fix_script = """#!/bin/bash
# Script para corrigir todos os problemas de string de template no arquivo server.js

# Caminho do arquivo
FILE="/app/server.js"

# Fazer backup do arquivo original
cp "$FILE" "$FILE.bak.script"

# Corrigir todas as strings de template problemáticas
# 1. Corrigir strings que começam com aspas duplas e terminam com backtick
sed -i 's/logger.error("\\([^"]*\\)${\\([^}]*\\)}`);/logger.error("\\1" + \\2);/g' "$FILE"

# 2. Corrigir strings que começam com backtick e terminam com aspas duplas
sed -i 's/logger.error(`\\([^`]*\\)${\\([^}]*\\)}");/logger.error("\\1" + \\2);/g' "$FILE"

# 3. Corrigir strings de template completas (começam e terminam com backtick)
sed -i 's/logger.error(`\\([^`]*\\)${\\([^}]*\\)}`);/logger.error("\\1" + \\2);/g' "$FILE"
sed -i 's/logger.warn(`\\([^`]*\\)${\\([^}]*\\)}`);/logger.warn("\\1" + \\2);/g' "$FILE"
sed -i 's/logger.info(`\\([^`]*\\)${\\([^}]*\\)}`);/logger.info("\\1" + \\2);/g' "$FILE"
sed -i 's/logger.debug(`\\([^`]*\\)${\\([^}]*\\)}`);/logger.debug("\\1" + \\2);/g' "$FILE"

# 4. Corrigir strings de template com múltiplas variáveis
sed -i 's/logger.error(`\\([^`]*\\)${\\([^}]*\\)}\\([^`]*\\)${\\([^}]*\\)}`);/logger.error("\\1" + \\2 + "\\3" + \\4);/g' "$FILE"
sed -i 's/logger.warn(`\\([^`]*\\)${\\([^}]*\\)}\\([^`]*\\)${\\([^}]*\\)}`);/logger.warn("\\1" + \\2 + "\\3" + \\4);/g' "$FILE"
sed -i 's/logger.info(`\\([^`]*\\)${\\([^}]*\\)}\\([^`]*\\)${\\([^}]*\\)}`);/logger.info("\\1" + \\2 + "\\3" + \\4);/g' "$FILE"
sed -i 's/logger.debug(`\\([^`]*\\)${\\([^}]*\\)}\\([^`]*\\)${\\([^}]*\\)}`);/logger.debug("\\1" + \\2 + "\\3" + \\4);/g' "$FILE"

# 5. Corrigir strings de template em objetos
sed -i 's/logger.error(`\\([^`]*\\)${\\([^}]*\\)}`, {/logger.error("\\1" + \\2, {/g' "$FILE"
sed -i 's/logger.warn(`\\([^`]*\\)${\\([^}]*\\)}`, {/logger.warn("\\1" + \\2, {/g' "$FILE"
sed -i 's/logger.info(`\\([^`]*\\)${\\([^}]*\\)}`, {/logger.info("\\1" + \\2, {/g' "$FILE"
sed -i 's/logger.debug(`\\([^`]*\\)${\\([^}]*\\)}`, {/logger.debug("\\1" + \\2, {/g' "$FILE"

echo "Correções aplicadas com sucesso!"
"""
        
        # Enviar o script para o contêiner
        print(Fore.YELLOW + "🔄 Enviando script para o contêiner...")
        await conn.run("echo '" + fix_script + "' > /tmp/fix_templates.sh")
        await conn.run("chmod +x /tmp/fix_templates.sh")
        await conn.run("docker cp /tmp/fix_templates.sh api-enviador:/tmp/fix_templates.sh")
        
        # Executar o script no contêiner
        print(Fore.YELLOW + "🔄 Executando script no contêiner...")
        result = await conn.run("docker exec api-enviador bash /tmp/fix_templates.sh")
        print(Fore.CYAN + "=== Resultado da execução do script ===")
        print(result.stdout)
        
        # Verificar se o arquivo foi corrigido
        print(Fore.YELLOW + "🔄 Tentando iniciar o servidor manualmente para verificar se os erros foram corrigidos...")
        result = await conn.run("docker exec api-enviador bash -c 'cd /app && node server.js' 2>&1 || echo 'Ainda há erros no servidor'")
        print(Fore.CYAN + "=== Resultado da Tentativa de Iniciar o Servidor ===")
        print(result.stdout)
        
        # Reiniciar o contêiner
        print(Fore.YELLOW + "🔄 Reiniciando o contêiner api-enviador...")
        await conn.run("docker restart api-enviador")
        print(Fore.GREEN + "✅ Contêiner reiniciado")
        
        # Aguardar um pouco para o serviço iniciar
        print(Fore.YELLOW + "🔄 Aguardando o serviço iniciar...")
        await asyncio.sleep(10)
        
        # Verificar status do contêiner
        print(Fore.YELLOW + "🔄 Verificando status do contêiner...")
        result = await conn.run("docker ps -a --filter name=api-enviador --format '{{.Names}} {{.Status}}'")
        print(Fore.CYAN + "=== Status do Contêiner api-enviador ===")
        print(result.stdout)
        
        # Verificar logs do contêiner
        print(Fore.YELLOW + "🔄 Verificando logs recentes do contêiner...")
        result = await conn.run("docker logs --tail 20 api-enviador")
        print(Fore.CYAN + "=== Logs Recentes do Contêiner api-enviador ===")
        print(result.stdout)
        
        # Fechar conexão SSH
        conn.close()
        print(Fore.GREEN + "✅ Correção concluída")
        
    except Exception as e:
        print(Fore.RED + f"❌ Erro: {e}")
        return False

async def main():
    await corrigir_servidor()

if __name__ == "__main__":
    asyncio.run(main())
