import sqlite3
import os
import json
import time
import asyncio
import aiohttp
import csv
from datetime import datetime
from tabulate import tabulate
from typing import List, Dict, Any, Optional, Tuple

# Database configuration
DB_PATH = "linodes.db"
CAMPAIGNS_DB = "campaigns.db"

class CampaignTracker:
    """Class to track and monitor email campaigns"""
    
    def __init__(self):
        """Initialize the campaign tracker and database"""
        self._init_db()
    
    def _init_db(self):
        """Initialize the campaigns database"""
        try:
            conn = sqlite3.connect(CAMPAIGNS_DB)
            c = conn.cursor()
            
            # Create campaigns table
            c.execute('''
                CREATE TABLE IF NOT EXISTS campaigns (
                    id TEXT PRIMARY KEY,
                    name TEXT,
                    status TEXT,
                    start_time TIMESTAMP,
                    last_updated TIMESTAMP,
                    end_time TIMESTAMP,
                    total_emails INTEGER DEFAULT 0,
                    sent_emails INTEGER DEFAULT 0,
                    failed_emails INTEGER DEFAULT 0,
                    pending_emails INTEGER DEFAULT 0,
                    server_ip TEXT,
                    server_label TEXT
                )
            ''')
            
            # Create campaign_details table for more detailed information
            c.execute('''
                CREATE TABLE IF NOT EXISTS campaign_details (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    campaign_id TEXT,
                    update_time TIMESTAMP,
                    status TEXT,
                    sent_emails INTEGER,
                    failed_emails INTEGER,
                    pending_emails INTEGER,
                    FOREIGN KEY (campaign_id) REFERENCES campaigns(id)
                )
            ''')
            
            conn.commit()
            conn.close()
            print("Campaign tracking database initialized successfully")
        except Exception as e:
            print(f"Error initializing campaign database: {e}")
    
    def load_campaigns_from_log(self, log_file="campaign_ids.log"):
        """Load campaign IDs from the log file and store them in the database"""
        if not os.path.exists(log_file):
            print(f"Log file {log_file} not found")
            return False
        
        try:
            # Get active servers from the database
            servers = self._get_active_servers()
            server_dict = {server['ip']: server for server in servers}
            
            # Read the log file
            with open(log_file, 'r') as f:
                campaigns_added = 0
                for line in f:
                    if line.startswith('#') or not line.strip():
                        continue
                    
                    parts = line.strip().split(',')
                    if len(parts) < 3:
                        continue
                    
                    server_ip = parts[0]
                    campaign_id = parts[1]
                    start_time = parts[2]
                    
                    # Check if this campaign is already in the database
                    if not self._campaign_exists(campaign_id):
                        # Get server label if available
                        server_label = server_dict.get(server_ip, {}).get('label', 'Unknown')
                        
                        # Add to database with default values
                        self._add_campaign(
                            campaign_id=campaign_id,
                            name=f"Campaign {campaign_id[:8]}",
                            status="unknown",
                            start_time=start_time,
                            server_ip=server_ip,
                            server_label=server_label
                        )
                        campaigns_added += 1
            
            print(f"Added {campaigns_added} new campaigns from log file")
            return True
        except Exception as e:
            print(f"Error loading campaigns from log: {e}")
            return False
    
    def _campaign_exists(self, campaign_id):
        """Check if a campaign already exists in the database"""
        try:
            conn = sqlite3.connect(CAMPAIGNS_DB)
            c = conn.cursor()
            c.execute('SELECT id FROM campaigns WHERE id = ?', (campaign_id,))
            result = c.fetchone()
            conn.close()
            return result is not None
        except Exception as e:
            print(f"Error checking if campaign exists: {e}")
            return False
    
    def _add_campaign(self, campaign_id, name, status, start_time, server_ip, server_label,
                     total_emails=0, sent_emails=0, failed_emails=0, pending_emails=0):
        """Add a new campaign to the database"""
        try:
            conn = sqlite3.connect(CAMPAIGNS_DB)
            c = conn.cursor()
            
            c.execute('''
                INSERT INTO campaigns (
                    id, name, status, start_time, last_updated, 
                    total_emails, sent_emails, failed_emails, pending_emails,
                    server_ip, server_label
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                campaign_id, name, status, start_time, datetime.now().isoformat(),
                total_emails, sent_emails, failed_emails, pending_emails,
                server_ip, server_label
            ))
            
            #conn.commit()
            conn.close()
            return True
        except Exception as e:
            print(f"Error adding campaign: {e}")
            return False
    
    def _get_active_servers(self):
        """Get active servers from the database"""
        try:
            conn = sqlite3.connect(DB_PATH)
            c = conn.cursor()
            c.execute('''
                SELECT id, linode_id, label, ip, url, status, email_status, token, created_at 
                FROM serversAtivos WHERE status = 1
            ''')
            
            servers = []
            for row in c.fetchall():
                servers.append({
                    'id': row[0],
                    'linode_id': row[1],
                    'label': row[2],
                    'ip': row[3],
                    'url': row[4],
                    'status': row[5],
                    'email_status': row[6],
                    'token': row[7],
                    'created_at': row[8]
                })
            
            conn.close()
            return servers
        except Exception as e:
            print(f"Error getting active servers: {e}")
            return []
    
    def register_campaign(self, campaign_data):
        """Register a new campaign from campaign data"""
        try:
            for campaign in campaign_data:
                server_ip = campaign['server']['ip']
                campaign_id = campaign['campaign_id']
                start_time = campaign['start_time']
                
                # Get server label
                servers = self._get_active_servers()
                server_dict = {server['ip']: server for server in servers}
                server_label = server_dict.get(server_ip, {}).get('label', 'Unknown')
                
                # Add to database
                self._add_campaign(
                    campaign_id=campaign_id,
                    name=f"Campaign {campaign_id[:8]}",
                    status="running",
                    start_time=start_time,
                    server_ip=server_ip,
                    server_label=server_label
                )
            
            return True
        except Exception as e:
            print(f"Error registering campaign: {e}")
            return False
    
    def update_campaign_status(self, campaign_id, status, stats=None):
        """Update the status and statistics of a campaign"""
        try:
            conn = sqlite3.connect(CAMPAIGNS_DB)
            c = conn.cursor()
            
            # Update campaign status
            if stats:
                c.execute('''
                    UPDATE campaigns SET 
                    status = ?, last_updated = ?,
                    total_emails = ?, sent_emails = ?, failed_emails = ?, pending_emails = ?
                    WHERE id = ?
                ''', (
                    status, datetime.now().isoformat(),
                    stats.get('total', 0), stats.get('sent', 0), 
                    stats.get('failed', 0), stats.get('pending', 0),
                    campaign_id
                ))
                
                # Add entry to campaign_details for historical tracking
                c.execute('''
                    INSERT INTO campaign_details (
                        campaign_id, update_time, status, 
                        sent_emails, failed_emails, pending_emails
                    ) VALUES (?, ?, ?, ?, ?, ?)
                ''', (
                    campaign_id, datetime.now().isoformat(), status,
                    stats.get('sent', 0), stats.get('failed', 0), stats.get('pending', 0)
                ))
            else:
                c.execute('''
                    UPDATE campaigns SET status = ?, last_updated = ? WHERE id = ?
                ''', (status, datetime.now().isoformat(), campaign_id))
            
            # If status is completed or failed, set end_time
            if status.lower() in ['completed', 'failed', 'finished']:
                c.execute('''
                    UPDATE campaigns SET end_time = ? WHERE id = ? AND end_time IS NULL
                ''', (datetime.now().isoformat(), campaign_id))
            
            conn.commit()
            conn.close()
            return True
        except Exception as e:
            print(f"Error updating campaign status: {e}")
            return False
    
    def get_campaigns(self, status=None, limit=50):
        """Get campaigns from the database, optionally filtered by status"""
        try:
            conn = sqlite3.connect(CAMPAIGNS_DB)
            conn.row_factory = sqlite3.Row
            c = conn.cursor()
            
            if status:
                c.execute('''
                    SELECT * FROM campaigns 
                    WHERE status = ? 
                    ORDER BY start_time DESC LIMIT ?
                ''', (status, limit))
            else:
                c.execute('''
                    SELECT * FROM campaigns 
                    ORDER BY start_time DESC LIMIT ?
                ''', (limit,))
            
            campaigns = [dict(row) for row in c.fetchall()]
            conn.close()
            return campaigns
        except Exception as e:
            print(f"Error getting campaigns: {e}")
            return []
    
    def get_campaign_details(self, campaign_id):
        """Get detailed information about a specific campaign"""
        try:
            conn = sqlite3.connect(CAMPAIGNS_DB)
            conn.row_factory = sqlite3.Row
            c = conn.cursor()
            
            # Get campaign info
            c.execute('SELECT * FROM campaigns WHERE id = ?', (campaign_id,))
            campaign = dict(c.fetchone())
            
            # Get campaign details/history
            c.execute('''
                SELECT * FROM campaign_details 
                WHERE campaign_id = ? 
                ORDER BY update_time DESC
            ''', (campaign_id,))
            
            details = [dict(row) for row in c.fetchall()]
            conn.close()
            
            return {
                'campaign': campaign,
                'details': details
            }
        except Exception as e:
            print(f"Error getting campaign details: {e}")
            return None
    
    async def check_campaign_status(self, server_ip, campaign_id):
        """Check the status of a campaign by making an API request to the server"""
        try:
            url = f"http://{server_ip}/campanha/{campaign_id}"
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, timeout=30) as response:
                    if response.status == 200:
                        data = await response.json()
                        stats = data.get('estatisticas', {})
                        
                        # Determine status based on pending emails
                        status = data.get('status', 'unknown')
                        if stats.get('pendentes', 0) == 0 and stats.get('total', 0) > 0:
                            status = 'completed'
                        
                        # Update campaign in database
                        self.update_campaign_status(
                            campaign_id, 
                            status,
                            {
                                'total': stats.get('total', 0),
                                'sent': stats.get('enviados', 0),
                                'failed': stats.get('falhas', 0),
                                'pending': stats.get('pendentes', 0)
                            }
                        )
                        
                        return {
                            'status': status,
                            'stats': stats,
                            'updated': True
                        }
                    else:
                        print(f"Error checking campaign {campaign_id}: HTTP {response.status}")
                        return {
                            'status': 'error',
                            'updated': False
                        }
        except Exception as e:
            print(f"Error checking campaign {campaign_id} on {server_ip}: {e}")
            return {
                'status': 'error',
                'updated': False
            }
    
    async def update_all_active_campaigns(self):
        """Update the status of all active campaigns"""
        # Get all running campaigns
        campaigns = self.get_campaigns(status='running')
        if not campaigns:
            print("No active campaigns found")
            return []
        
        # Check status of each campaign
        tasks = []
        for campaign in campaigns:
            server_ip = campaign['server_ip']
            campaign_id = campaign['id']
            tasks.append(self.check_campaign_status(server_ip, campaign_id))
        
        # Wait for all tasks to complete
        results = await asyncio.gather(*tasks)
        return results
    
    def generate_campaign_report(self, campaign_id=None, output_file=None):
        """Generate a report for a specific campaign or all campaigns"""
        try:
            conn = sqlite3.connect(CAMPAIGNS_DB)
            conn.row_factory = sqlite3.Row
            c = conn.cursor()
            
            if campaign_id:
                # Report for a specific campaign
                c.execute('SELECT * FROM campaigns WHERE id = ?', (campaign_id,))
                campaign = dict(c.fetchone())
                
                if not campaign:
                    print(f"Campaign {campaign_id} not found")
                    return None
                
                # Get campaign details/history
                c.execute('''
                    SELECT * FROM campaign_details 
                    WHERE campaign_id = ? 
                    ORDER BY update_time ASC
                ''', (campaign_id,))
                
                details = [dict(row) for row in c.fetchall()]
                
                # Generate report
                report = {
                    'campaign': campaign,
                    'details': details,
                    'summary': {
                        'duration': self._calculate_duration(campaign),
                        'success_rate': self._calculate_success_rate(campaign),
                        'emails_per_minute': self._calculate_emails_per_minute(campaign)
                    }
                }
                
                # Export to CSV if requested
                if output_file:
                    self._export_campaign_report_to_csv(report, output_file)
                
                return report
            else:
                # Report for all campaigns
                c.execute('SELECT * FROM campaigns ORDER BY start_time DESC')
                campaigns = [dict(row) for row in c.fetchall()]
                
                # Generate summary for each campaign
                for campaign in campaigns:
                    campaign['duration'] = self._calculate_duration(campaign)
                    campaign['success_rate'] = self._calculate_success_rate(campaign)
                    campaign['emails_per_minute'] = self._calculate_emails_per_minute(campaign)
                
                # Export to CSV if requested
                if output_file:
                    self._export_campaigns_to_csv(campaigns, output_file)
                
                return campaigns
        except Exception as e:
            print(f"Error generating campaign report: {e}")
            return None
        finally:
            conn.close()
    
    def _calculate_duration(self, campaign):
        """Calculate the duration of a campaign in minutes"""
        try:
            start_time = datetime.fromisoformat(campaign['start_time'])
            
            if campaign['end_time']:
                end_time = datetime.fromisoformat(campaign['end_time'])
            else:
                end_time = datetime.now()
            
            duration = (end_time - start_time).total_seconds() / 60
            return round(duration, 2)
        except:
            return 0
    
    def _calculate_success_rate(self, campaign):
        """Calculate the success rate of a campaign"""
        total = campaign['total_emails']
        if total == 0:
            return 0
        
        sent = campaign['sent_emails']
        return round((sent / total) * 100, 2)
    
    def _calculate_emails_per_minute(self, campaign):
        """Calculate the emails sent per minute"""
        duration = self._calculate_duration(campaign)
        if duration == 0:
            return 0
        
        sent = campaign['sent_emails']
        return round(sent / duration, 2)
    
    def _export_campaign_report_to_csv(self, report, output_file):
        """Export a campaign report to CSV"""
        try:
            # Export campaign details
            campaign = report['campaign']
            details = report['details']
            summary = report['summary']
            
            with open(output_file, 'w', newline='') as f:
                writer = csv.writer(f)
                
                # Write campaign info
                writer.writerow(['Campaign Report'])
                writer.writerow(['ID', campaign['id']])
                writer.writerow(['Name', campaign['name']])
                writer.writerow(['Status', campaign['status']])
                writer.writerow(['Server IP', campaign['server_ip']])
                writer.writerow(['Server Label', campaign['server_label']])
                writer.writerow(['Start Time', campaign['start_time']])
                writer.writerow(['End Time', campaign['end_time'] or 'N/A'])
                writer.writerow(['Duration (minutes)', summary['duration']])
                writer.writerow(['Success Rate (%)', summary['success_rate']])
                writer.writerow(['Emails Per Minute', summary['emails_per_minute']])
                writer.writerow(['Total Emails', campaign['total_emails']])
                writer.writerow(['Sent Emails', campaign['sent_emails']])
                writer.writerow(['Failed Emails', campaign['failed_emails']])
                writer.writerow(['Pending Emails', campaign['pending_emails']])
                
                # Write history
                writer.writerow([])
                writer.writerow(['History'])
                writer.writerow(['Time', 'Status', 'Sent', 'Failed', 'Pending'])
                
                for detail in details:
                    writer.writerow([
                        detail['update_time'],
                        detail['status'],
                        detail['sent_emails'],
                        detail['failed_emails'],
                        detail['pending_emails']
                    ])
            
            print(f"Campaign report exported to {output_file}")
            return True
        except Exception as e:
            print(f"Error exporting campaign report to CSV: {e}")
            return False
    
    def _export_campaigns_to_csv(self, campaigns, output_file):
        """Export a list of campaigns to CSV"""
        try:
            with open(output_file, 'w', newline='') as f:
                writer = csv.writer(f)
                
                # Write header
                writer.writerow([
                    'ID', 'Name', 'Status', 'Server IP', 'Server Label',
                    'Start Time', 'End Time', 'Duration (min)', 'Success Rate (%)',
                    'Emails/Min', 'Total Emails', 'Sent', 'Failed', 'Pending'
                ])
                
                # Write data
                for campaign in campaigns:
                    writer.writerow([
                        campaign['id'],
                        campaign['name'],
                        campaign['status'],
                        campaign['server_ip'],
                        campaign['server_label'],
                        campaign['start_time'],
                        campaign['end_time'] or 'N/A',
                        campaign['duration'],
                        campaign['success_rate'],
                        campaign['emails_per_minute'],
                        campaign['total_emails'],
                        campaign['sent_emails'],
                        campaign['failed_emails'],
                        campaign['pending_emails']
                    ])
            
            print(f"Campaigns exported to {output_file}")
            return True
        except Exception as e:
            print(f"Error exporting campaigns to CSV: {e}")
            return False

# Helper functions for displaying campaign data in the terminal
def display_campaigns_table(campaigns):
    """Display campaigns in a formatted table"""
    if not campaigns:
        print("No campaigns found")
        return
    
    # Prepare data for tabulate
    headers = [
        "ID", "Name", "Status", "Server", "Start Time", 
        "Progress", "Sent", "Failed", "Pending"
    ]
    
    rows = []
    for campaign in campaigns:
        # Calculate progress percentage
        total = campaign['total_emails']
        sent = campaign['sent_emails']
        progress = f"{(sent/total)*100:.1f}%" if total > 0 else "0%"
        
        # Format start time
        try:
            start_time = datetime.fromisoformat(campaign['start_time']).strftime("%Y-%m-%d %H:%M")
        except:
            start_time = campaign['start_time']
        
        # Add row
        rows.append([
            campaign['id'][:8],  # Shortened ID
            campaign['name'],
            campaign['status'],
            campaign['server_label'] or campaign['server_ip'],
            start_time,
            progress,
            campaign['sent_emails'],
            campaign['failed_emails'],
            campaign['pending_emails']
        ])
    
    # Print table
    print(tabulate(rows, headers=headers, tablefmt="grid"))

async def monitor_campaigns(tracker, interval=60, max_iterations=None):
    """Monitor active campaigns with periodic updates"""
    iteration = 0
    try:
        while True:
            # Check if we've reached the maximum number of iterations
            if max_iterations is not None and iteration >= max_iterations:
                break
            
            # Clear screen
            os.system('cls' if os.name == 'nt' else 'clear')
            
            # Update campaign status
            print("Updating campaign status...")
            await tracker.update_all_active_campaigns()
            
            # Get and display active campaigns
            campaigns = tracker.get_campaigns(status='running')
            print(f"\n=== Active Campaigns ({len(campaigns)}) ===")
            display_campaigns_table(campaigns)
            
            # Show last update time
            print(f"\nLast updated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"Next update in {interval} seconds. Press Ctrl+C to stop monitoring.")
            
            # Wait for next update
            iteration += 1
            await asyncio.sleep(interval)
    except KeyboardInterrupt:
        print("\nMonitoring stopped by user")
    except Exception as e:
        print(f"Error in monitoring: {e}")

# Function to be called from main.py
async def monitor_campaigns_menu():
    """Menu function for monitoring campaigns"""
    tracker = CampaignTracker()
    
    # Load campaigns from log file
    tracker.load_campaigns_from_log()
    
    while True:
        print("\n=== Campaign Monitoring ===")
        print("1. Monitor active campaigns")
        print("2. View all campaigns")
        print("3. View campaign details")
        print("4. Generate campaign report")
        print("5. Export all campaigns to CSV")
        print("0. Return to main menu")
        
        choice = input("\nEnter your choice: ")
        
        if choice == "1":
            # Monitor active campaigns
            print("\nStarting campaign monitoring (updates every 60 seconds)...")
            print("Press Ctrl+C to stop monitoring")
            await monitor_campaigns(tracker, interval=60)
        
        elif choice == "2":
            # View all campaigns
            status_filter = input("Filter by status (running, completed, all): ").lower()
            if status_filter == "all":
                status_filter = None
            
            campaigns = tracker.get_campaigns(status=status_filter)
            print(f"\n=== Campaigns ({len(campaigns)}) ===")
            display_campaigns_table(campaigns)
            
            # Wait for user to press enter
            input("\nPress Enter to continue...")
        
        elif choice == "3":
            # View campaign details
            campaign_id = input("Enter campaign ID (or part of it): ")
            
            # If user entered a partial ID, try to find a match
            if len(campaign_id) < 36:  # UUID is 36 chars
                campaigns = tracker.get_campaigns()
                for campaign in campaigns:
                    if campaign_id in campaign['id']:
                        campaign_id = campaign['id']
                        print(f"Found matching campaign: {campaign_id}")
                        break
            
            details = tracker.get_campaign_details(campaign_id)
            if details:
                campaign = details['campaign']
                history = details['details']
                
                print(f"\n=== Campaign Details ===")
                print(f"ID: {campaign['id']}")
                print(f"Name: {campaign['name']}")
                print(f"Status: {campaign['status']}")
                print(f"Server: {campaign['server_label']} ({campaign['server_ip']})")
                print(f"Start Time: {campaign['start_time']}")
                print(f"End Time: {campaign['end_time'] or 'N/A'}")
                print(f"Total Emails: {campaign['total_emails']}")
                print(f"Sent Emails: {campaign['sent_emails']}")
                print(f"Failed Emails: {campaign['failed_emails']}")
                print(f"Pending Emails: {campaign['pending_emails']}")
                
                # Calculate some statistics
                duration = tracker._calculate_duration(campaign)
                success_rate = tracker._calculate_success_rate(campaign)
                emails_per_minute = tracker._calculate_emails_per_minute(campaign)
                
                print(f"\n=== Statistics ===")
                print(f"Duration: {duration} minutes")
                print(f"Success Rate: {success_rate}%")
                print(f"Emails Per Minute: {emails_per_minute}")
                
                # Show history if available
                if history:
                    print(f"\n=== History ({len(history)} updates) ===")
                    headers = ["Time", "Status", "Sent", "Failed", "Pending"]
                    rows = []
                    
                    for entry in history[:10]:  # Show only the last 10 updates
                        try:
                            update_time = datetime.fromisoformat(entry['update_time']).strftime("%Y-%m-%d %H:%M:%S")
                        except:
                            update_time = entry['update_time']
                        
                        rows.append([
                            update_time,
                            entry['status'],
                            entry['sent_emails'],
                            entry['failed_emails'],
                            entry['pending_emails']
                        ])
                    
                    print(tabulate(rows, headers=headers, tablefmt="grid"))
                    
                    if len(history) > 10:
                        print(f"... and {len(history) - 10} more updates")
            else:
                print(f"Campaign {campaign_id} not found")
            
            # Wait for user to press enter
            input("\nPress Enter to continue...")
        
        elif choice == "4":
            # Generate campaign report
            campaign_id = input("Enter campaign ID (or part of it, leave empty for all campaigns): ")
            
            # If user entered a partial ID, try to find a match
            if campaign_id and len(campaign_id) < 36:  # UUID is 36 chars
                campaigns = tracker.get_campaigns()
                for campaign in campaigns:
                    if campaign_id in campaign['id']:
                        campaign_id = campaign['id']
                        print(f"Found matching campaign: {campaign_id}")
                        break
            
            # Ask for output file
            output_file = input("Export to CSV file (leave empty to display only): ")
            
            if campaign_id:
                report = tracker.generate_campaign_report(campaign_id, output_file)
                if report:
                    campaign = report['campaign']
                    summary = report['summary']
                    
                    print(f"\n=== Campaign Report ===")
                    print(f"ID: {campaign['id']}")
                    print(f"Name: {campaign['name']}")
                    print(f"Status: {campaign['status']}")
                    print(f"Server: {campaign['server_label']} ({campaign['server_ip']})")
                    print(f"Start Time: {campaign['start_time']}")
                    print(f"End Time: {campaign['end_time'] or 'N/A'}")
                    print(f"Duration: {summary['duration']} minutes")
                    print(f"Success Rate: {summary['success_rate']}%")
                    print(f"Emails Per Minute: {summary['emails_per_minute']}")
                    print(f"Total Emails: {campaign['total_emails']}")
                    print(f"Sent Emails: {campaign['sent_emails']}")
                    print(f"Failed Emails: {campaign['failed_emails']}")
                    print(f"Pending Emails: {campaign['pending_emails']}")
                else:
                    print(f"Campaign {campaign_id} not found")
            else:
                campaigns = tracker.generate_campaign_report(None, output_file)
                if campaigns:
                    print(f"\n=== Campaign Summary ({len(campaigns)} campaigns) ===")
                    
                    # Prepare data for tabulate
                    headers = [
                        "ID", "Name", "Status", "Server", 
                        "Duration", "Success", "Emails/Min", "Total"
                    ]
                    
                    rows = []
                    for campaign in campaigns:
                        rows.append([
                            campaign['id'][:8],
                            campaign['name'],
                            campaign['status'],
                            campaign['server_label'] or campaign['server_ip'],
                            f"{campaign['duration']} min",
                            f"{campaign['success_rate']}%",
                            campaign['emails_per_minute'],
                            campaign['total_emails']
                        ])
                    
                    print(tabulate(rows, headers=headers, tablefmt="grid"))
            
            # Wait for user to press enter
            input("\nPress Enter to continue...")
        
        elif choice == "5":
            # Export all campaigns to CSV
            output_file = input("Enter output file name (default: campaigns_export.csv): ") or "campaigns_export.csv"
            tracker.generate_campaign_report(None, output_file)
            
            # Wait for user to press enter
            input("\nPress Enter to continue...")
        
        elif choice == "0":
            # Return to main menu
            break
        
        else:
            print("Invalid choice. Please try again.")

# Hook function to register a new campaign when it's created
def register_new_campaign(campaign_data):
    """Register a new campaign when it's created"""
    tracker = CampaignTracker()
    return tracker.register_campaign(campaign_data)
