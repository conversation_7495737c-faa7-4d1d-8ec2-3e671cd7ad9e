import requests
import asyncio
import aiohttp
import asyncssh
import base64
import json
import os
from colorama import Fore, Style, init
from datetime import datetime, timedelta
from database_manager import (
    initialize_database, store_machine_data, mark_machine_configured,
    is_machine_configured, add_active_server, update_server_status
)
import sqlite3
import random
import sys
import traceback


init(autoreset=True)

MAX_VPS = 23 # Número máximo de VPS permitidas na conta

# Arquivo de tokens e proxies
# Formato do arquivo: cada linha deve ter um token e um proxy separados por |
# Exemplo: TStk10Irm1CnPki....kQwt|***************************:port
TOKEN_PROXY_FILE = "tokens_proxies.txt"

# Inicializar as variáveis globais vazias
TOKEN_PROXY_MAP = {}
TOKENS = []
PROXIES = []

# Função para carregar tokens e proxies do arquivo
def load_token_proxy_map():
    """Carrega tokens e proxies de um arquivo de texto no formato token|proxy"""
    token_proxy_map = {}
    
    # Criar arquivo se não existir
    if not os.path.exists(TOKEN_PROXY_FILE):
        print(Fore.YELLOW + f"⚠️ Arquivo {TOKEN_PROXY_FILE} não encontrado. Criando arquivo modelo.")
        with open(TOKEN_PROXY_FILE, 'w') as f:
            f.write("# Formato: token|proxy\n")
            f.write("# Exemplo: TStk10Irm1CnPkiY3Xkwlu2MGqQW0FgQHLWw1UAyBs5tkcSvZlFhf5yLbhlKZG0MJdhGpLgsZvVuMiQiSDalrIRgHtbi4IjgkQwt|***************************:port\n")
            f.write("# Adicione seus tokens e proxies abaixo, um por linha\n")
            f.write("# Tokens inválidos (403) serão automaticamente removidos\n\n")
            f.write("# TStk10Irm1CnPkiY3Xkwlu2MGqQW0FgQHLWw1UAyBs5tkcSvZlFhf5yLbhlKZG0MJdhGpLgsZvVuMiQiSDalrIRgHtbi4IjgkQwt|http://matttunner:DtmIxzoki8@************:50100\n")
        print(Fore.YELLOW + f"⚠️ Arquivo modelo criado. Por favor, edite {TOKEN_PROXY_FILE} e adicione seus tokens e proxies antes de continuar.")
        return {}
    
    try:
        with open(TOKEN_PROXY_FILE, 'r') as f:
            for line in f:
                line = line.strip()
                # Ignorar linhas vazias ou comentários
                if not line or line.startswith('#'):
                    continue
                
                # Dividir a linha pelo separador |
                parts = line.split('|')
                if len(parts) == 2:
                    token, proxy = parts
                    token_proxy_map[token] = proxy
                else:
                    print(Fore.RED + f"❌ Formato inválido na linha: {line}")
        
        print(Fore.GREEN + f"✅ Carregados {len(token_proxy_map)} tokens do arquivo {TOKEN_PROXY_FILE}")
        return token_proxy_map
    except Exception as e:
        print(Fore.RED + f"❌ Erro ao carregar arquivo de tokens: {str(e)}")
        return {}

# Função para remover token do arquivo
async def remove_token_from_file(token):
    """Remove um token do arquivo de tokens e proxies"""
    try:
        # Ler todas as linhas do arquivo
        with open(TOKEN_PROXY_FILE, 'r') as f:
            lines = f.readlines()
        
        # Filtrar linhas, removendo a que contém o token
        filtered_lines = [line for line in lines if not line.strip().startswith(token)]
        
        # Se o número de linhas for diferente, significa que o token foi encontrado e removido
        if len(lines) != len(filtered_lines):
            # Escrever as linhas filtradas de volta ao arquivo
            with open(TOKEN_PROXY_FILE, 'w') as f:
                f.writelines(filtered_lines)
            print(Fore.GREEN + f"✅ Token {token[:5]}... removido do arquivo {TOKEN_PROXY_FILE}")
            return True
        else:
            print(Fore.YELLOW + f"⚠️ Token {token[:5]}... não encontrado no arquivo {TOKEN_PROXY_FILE}")
            return False
    except Exception as e:
        print(Fore.RED + f"❌ Erro ao remover token do arquivo: {str(e)}")
        return False

async def remove_all_vms_for_token(token, proxy=None):
    """Remove todas as VMs associadas a um token do banco de dados e da plataforma Doprax"""
    print(Fore.YELLOW + f"🗑️ Removendo todas as VMs associadas ao token {token[:5]}...")
    
    # 1. Obter todas as VMs associadas ao token do banco de dados
    with sqlite3.connect('unified_system.db') as conn:
        cursor = conn.cursor()
        cursor.execute("SELECT vm_code, ip_address FROM machines WHERE provider_token = ?", (token,))
        vms = cursor.fetchall()
    
    if not vms:
        print(Fore.YELLOW + f"⚠️ Nenhuma VM encontrada para o token {token[:5]}...")
        return False
    
    print(Fore.YELLOW + f"🔍 Encontradas {len(vms)} VMs associadas ao token {token[:5]}...")
    
    # 2. Tentar deletar cada VM da plataforma Doprax
    deletion_results = []
    for vm_code, ip_address in vms:
        print(Fore.YELLOW + f"🗑️ Deletando VM {ip_address} (Código: {vm_code})...")
        try:
            if proxy:
                # Se temos um proxy, tenta deletar via API
                result = await deletar_vps(vm_code, token, proxy)
                deletion_results.append(result)
            else:
                # Se não temos proxy, apenas registra que não conseguimos deletar via API
                print(Fore.YELLOW + f"⚠️ Não foi possível deletar VM {vm_code} da plataforma (sem proxy)")
                deletion_results.append(False)
        except Exception as e:
            print(Fore.RED + f"❌ Erro ao deletar VM {vm_code}: {str(e)}")
            deletion_results.append(False)
    
    # 3. Remover todas as VMs do banco de dados, independente do resultado da deleção na plataforma
    # (Já que o token não tem crédito, essas VMs estarão desligadas de qualquer forma)
    with sqlite3.connect('unified_system.db') as conn:
        cursor = conn.cursor()
        
        # Obter todos os IPs para limpar tabela de servidores ativos
        cursor.execute("SELECT ip_address FROM machines WHERE provider_token = ?", (token,))
        ips = [ip for (ip,) in cursor.fetchall()]
        
        # Remover da tabela machines
        cursor.execute("DELETE FROM machines WHERE provider_token = ?", (token,))
        machines_deleted = cursor.rowcount
        
        # Remover da tabela serversAtivosEnvio
        for ip in ips:
            cursor.execute("DELETE FROM serversAtivosEnvio WHERE url = ?", (f"http://{ip}",))
        
        conn.commit()
    
    print(Fore.GREEN + f"✅ Removidas {machines_deleted} VMs do banco de dados para o token {token[:5]}...")
    
    # 4. Calcular estatísticas
    successful_api_deletions = sum(1 for result in deletion_results if result)
    if successful_api_deletions > 0:
        print(Fore.GREEN + f"✅ {successful_api_deletions}/{len(vms)} VMs deletadas com sucesso da plataforma Doprax")
    
    return True

# Carregar tokens e proxies inicialmente
TOKEN_PROXY_MAP = load_token_proxy_map()
# Listas de tokens e proxies para compatibilidade
TOKENS = list(TOKEN_PROXY_MAP.keys())
PROXIES = list(TOKEN_PROXY_MAP.values())

# Configurações de VPS
# Configurações de VPS
VPS_CONFIGURATIONS = [
    {"name":"Digitalocean-USA-DO1","provider_name":"Digitalocean","machine_type_code":"53a1b74b-d79c-4789-8b37-911ddabdca8e","location_code":"94d429f8-2bbf-48d4-8f84-b5ffaeb801cd","os_name":"Ubuntu","os_slug":"ubuntu_22_04","location_country":"USA","vm_type":"DO1"},
    {"name":"Digitalocean-USA-DO2","provider_name":"Digitalocean","machine_type_code":"24ef2091-f4f1-49fe-bf56-d3ef1733dc2e","location_code":"850d3813-c02b-446d-8c4c-da293a3b5f5d","os_name":"Ubuntu","os_slug":"ubuntu_22_04","location_country":"USA","vm_type":"DO2"}
]
# Inicializar o banco de dados
initialize_database()

# Função para obter a proxy correspondente a um token
def get_proxy_for_token(token):
    """Obter a proxy correspondente ao token fornecido"""
    if token in TOKEN_PROXY_MAP:
        return TOKEN_PROXY_MAP[token]
    return PROXIES[0] if PROXIES else None

def obter_ips_entregue():
    """Obtém os IPs das VMs marcadas como 'Entregue' na tabela serversAtivosEnvio."""
    ips_entregue = set()
    with sqlite3.connect('unified_system.db') as conn:
        cursor = conn.cursor()
        cursor.execute("SELECT url FROM serversAtivosEnvio WHERE status = 'Entregue'")
        for row in cursor.fetchall():
            ip = row[0].replace("http://", "")  # Remove o "http://" para obter o IP
            ips_entregue.add(ip)
    print(Fore.CYAN + f"✅ {len(ips_entregue)} IPs marcados como 'Entregue' encontrados.")
    return ips_entregue


async def obter_credenciais(token, proxy):
    """Obtém as credenciais das VMs de uma conta, ignorando as que já estão marcadas como 'Entregue'."""
    global TOKEN_PROXY_MAP, TOKENS, PROXIES
    
    cookies = {
        'cw_conversation': 'eyJhbGciOiJIUzI1NiJ9...',
        'g_state': '{"i_l":0}',
        '_ga_R7ZH47R58S': 'GS1.1.**********.1.0.**********.0.0.0',
        '_ga': 'GA1.1.901258794.**********',
        'stamp': 'OlNLCYJ5aAcFcMjHZngLJHY6ji3QnvmzY2A9givdWMWX1rYtP1',
    }

    headers = {
        'Host': 'www.doprax.com',
        'user-agent': 'Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:135.0) Gecko/******** Firefox/135.0',
        'accept': 'application/json, text/plain, */*',
        'accept-language': 'en-US,en;q=0.5',
        'referer': 'https://www.doprax.com/v/me/',
        'authorization': f'Bearer {token}',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-origin',
        'pragma': 'no-cache',
        'cache-control': 'no-cache',
    }

    try:
        # Criar um objeto TCPConnector com verify_ssl=False
        connector = aiohttp.TCPConnector(verify_ssl=False)

        # Modificar todas as criações de ClientSession para usar o connector
        async with aiohttp.ClientSession(connector=connector) as session:
            async with session.get('https://www.doprax.com/api/v1/vms/', headers=headers, proxy=proxy) as response:
                if response.status == 403:
                    print(Fore.RED + f"❌ Token {token[:5]}... retornou 403 (Proibido). Removendo este token do arquivo.")
                    await remove_token_from_file(token)
                    # Atualizar o mapeamento em memória
                    if token in TOKEN_PROXY_MAP:
                        del TOKEN_PROXY_MAP[token]
                        TOKENS = list(TOKEN_PROXY_MAP.keys())
                        PROXIES = list(TOKEN_PROXY_MAP.values())
                    return []
                
                # Verificar se há mensagem de crédito insuficiente
                response_text = await response.text()
                if "You don't have enough credit" in response_text or "Insufficient credit" in response_text:
                    print(Fore.RED + f"❌ Token {token[:5]}... não tem crédito suficiente. Removendo este token do arquivo.")
                    await remove_token_from_file(token)
                    # Remover todas as VMs associadas a este token
                    await remove_all_vms_for_token(token, proxy)
                    # Atualizar o mapeamento em memória
                    if token in TOKEN_PROXY_MAP:
                        del TOKEN_PROXY_MAP[token]
                        TOKENS = list(TOKEN_PROXY_MAP.keys())
                        PROXIES = list(TOKEN_PROXY_MAP.values())
                    return []

                vm_list = (await response.json()).get("vm_list", [])
                credentials = []

                # Obter IPs marcados como "Entregue"
                ips_entregue = obter_ips_entregue()

                for vm in vm_list:
                    # Verificar se o IP existe e não está vazio
                    if not vm.get("ipv4"):
                        print(Fore.YELLOW + f"⚠️ Ignorando VM sem IP válido (vm_code: {vm.get('vm_code', 'desconhecido')})")
                        continue
                        
                    ip = vm["ipv4"]
                    
                    # Validação adicional para garantir que o IP não esteja vazio
                    if not ip or ip.strip() == "":
                        print(Fore.YELLOW + f"⚠️ Ignorando VM com IP vazio (vm_code: {vm.get('vm_code', 'desconhecido')})")
                        continue
                        
                    if ip in ips_entregue:
                        print(Fore.YELLOW + f"⚠️ Ignorando VM {ip} (já está marcada como 'Entregue').")
                        continue

                    vm_code = vm.get("vm_code")
                    if not vm_code:
                        print(Fore.YELLOW + f"⚠️ Ignorando VM {ip} sem código VM válido.")
                        continue
                        
                    print(Fore.CYAN + f"🔍 Obtendo senha para VM {vm_code} ({ip})")

                    password_headers = headers.copy()
                    password_headers['referer'] = f'https://www.doprax.com/v/virtual-machines/{vm_code}/'
                    async with session.get(
                        f'https://www.doprax.com/api/v1/vms/{vm_code}/password/',
                        cookies=cookies,
                        headers=password_headers,
                        proxy=proxy
                    ) as password_response:
                        if password_response.status == 200:
                            password = (await password_response.json()).get("temp_pass", "N/A")
                            print(Fore.GREEN + f"✅ Senha obtida com sucesso para {ip}")
                        else:
                            password = "N/A"
                            print(Fore.RED + f"❌ Erro ao obter senha para {vm_code}. Status: {password_response.status}")

                    # Somente armazenar se tiver IP válido, vm_code válido e senha
                    if ip and vm_code and password:
                        store_machine_data("Doprax", ip, password, vm_code, token)
                        credentials.append(f"{ip}:{password}:{vm_code}\n")
                    else:
                        print(Fore.RED + f"❌ Ignorando VM com dados incompletos: IP={ip}, VM_CODE={vm_code}")

                return credentials
    except Exception as e:
        print(Fore.RED + f"❌ Exceção ao obter credenciais: {type(e).__name__}: {str(e)}")
        return []
    
def store_machine_data(provider, ip_address, password, vm_code, provider_token):
    """Armazena os dados da máquina no banco de dados."""
    with sqlite3.connect('unified_system.db') as conn:
        cursor = conn.cursor()
        cursor.execute("""
            INSERT INTO machines (provider, ip_address, password, vm_code, provider_token)
            VALUES (?, ?, ?, ?, ?)
            ON CONFLICT(ip_address) DO UPDATE SET
                password = excluded.password,
                vm_code = excluded.vm_code,
                provider_token = excluded.provider_token
        """, (provider, ip_address, password, vm_code, provider_token))
        conn.commit()
            
async def criar_droplet(token, proxy):
    """Cria uma nova VM, ignorando contas que já atingiram o limite de VPS."""
    cookies = {
        'cw_conversation': 'eyJhbGciOiJIUzI1NiJ9...',
        'g_state': '{"i_l":0}',
        'ga_R7ZH47R58S': 'GS1.1.**********.1.0.**********.0.0.0',
        '_ga': 'GA1.1.901258794.**********',
        'stamp': 'OlNLCYJ5aAcFcMjHZngLJHY6ji3QnvmzY2A9givdWMWX1rYtP1',
    }

    headers = {
        'Host': 'www.doprax.com',
        'user-agent': 'Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:135.0) Gecko/******** Firefox/135.0',
        'accept': 'application/json, text/plain, */*',
        'accept-language': 'en-US,en;q=0.5',
        'referer': 'https://www.doprax.com/v/virtual-machines/add-new/',
        'content-type': 'application/json',
        'authorization': f'Bearer {token}',
        'origin': 'https://www.doprax.com',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-origin',
        'priority': 'u=0',
        'te': 'trailers',
        'pragma': 'no-cache',
        'cache-control': 'no-cache',
    }

    vps_config = random.choice(VPS_CONFIGURATIONS)
    print(Fore.CYAN + f"🌍 Criando VPS em {vps_config['location_country']} usando {vps_config['provider_name']} ({vps_config['vm_type']})")

    try:
        # Criar um objeto TCPConnector com verify_ssl=False
        connector = aiohttp.TCPConnector(verify_ssl=False)

        # Modificar todas as criações de ClientSession para usar o connector
        async with aiohttp.ClientSession(connector=connector) as session:
            async with session.post(
                'https://www.doprax.com/api/v1/vms/',
                cookies=cookies,
                headers=headers,
                json=vps_config,
                proxy=proxy
            ) as response:
                response_text = await response.text()

                if "You can only create 10 VMs" in response_text:
                    print(Fore.RED + f"❌ Limite de 10 VMs atingido na conta (token: {token[:5]}...)")
                    return False

                if "You don't have enough credit" in response_text:
                    print(Fore.RED + f"❌ Token {token[:5]}... não tem crédito suficiente. Removendo este token do arquivo.")
                    await remove_token_from_file(token)
                    # Atualizar o mapeamento em memória
                    if token in TOKEN_PROXY_MAP:
                        # Remover todas as VMs associadas a este token
                        await remove_all_vms_for_token(token, proxy)
                        # Remover token do mapeamento em memória
                        del TOKEN_PROXY_MAP[token]
                        TOKENS = list(TOKEN_PROXY_MAP.keys())
                        PROXIES = list(TOKEN_PROXY_MAP.values())
                    return False

                if response.status == 201:
                    print(Fore.GREEN + f"✅ Droplet criada com sucesso em {vps_config['location_country']}!")
                    return True
                else:
                    print(Fore.RED + f"❌{token[:5]}... Erro ao criar droplet em {vps_config['location_country']}. Status: {response.status}")
                    with open('tokens_removidos.txt', 'a') as f:
                        f.write(f"{token} - {vps_config['location_country']} - {vps_config['vm_type']} - {response_text[:200]}... status: {response.status}\n")
                    print(Fore.RED + f"Resposta: {response_text[:200]}...")
                    return False
    except Exception as e:
        print(Fore.RED + f"❌ Exceção ao criar VM: {type(e).__name__}: {str(e)}")
        return False

async def monitor_postfix(ip: str, email: str, retries: int = 10, max_wait_time: int = 180) -> dict:
    """
    Monitor postfix status with extended retry logic and increasing wait times.
    """
    connector = aiohttp.TCPConnector(verify_ssl=False)
    async with aiohttp.ClientSession(connector=connector) as session:
        last_error = None
        last_response = None
        
        print(Fore.CYAN + f"🔍 [{ip}] Iniciando monitoramento do Postfix (até {retries} tentativas)")
        
        for attempt in range(1, retries + 1):
            wait_time = 5
            
            try:
                if attempt > 1:
                    print(Fore.YELLOW + f"⏳ [{ip}] Aguardando {wait_time}s antes da tentativa {attempt}/{retries}...")
                    await asyncio.sleep(wait_time)
                
                print(Fore.CYAN + f"🔍 [{ip}] Tentativa {attempt}/{retries} de verificar status Postfix...")
                async with session.get(f'http://{ip}/postfix-status', ssl=False, timeout=30) as resp:
                    if resp.status == 200:
                        data = await resp.json()
                        last_response = data

                        # Verifica se a frase "delivery temporarily suspended" está em algum valor do JSON
                        if any("delivery temporarily suspended" in str(value).lower() for value in data.values()):
                            print(Fore.RED + f"❌ [{ip}] Status Postfix: Delivery temporarily suspended (tentativa {attempt}/{retries})")
                            return {'status': 'failed', 'emailStatus': 'Falha - Delivery temporarily suspended'}
                        
                        # Verifica status específicos
                        if data.get('emailStatus') == 'Entregue':
                            print(Fore.GREEN + f"✅ [{ip}] Status Postfix: Entregue (tentativa {attempt}/{retries})")
                            return data
                        elif data.get('emailStatus') in ['Limite temporário atingido', 'Falha - Email retornou']:
                            print(Fore.RED + f"❌ [{ip}] Status Postfix: {data.get('emailStatus')} (tentativa {attempt}/{retries})")
                            return data
                        else:
                            print(Fore.YELLOW + f"⚠️ [{ip}] Status intermediário: {data.get('emailStatus')} (continuando...)")
                    else:
                        print(Fore.RED + f"⚠️ [{ip}] Resposta HTTP inesperada: {resp.status}")
                        last_error = f"HTTP {resp.status}"
            
            except asyncio.TimeoutError:
                print(Fore.YELLOW + f"⏱️ [{ip}] Timeout ao verificar Postfix (tentativa {attempt}/{retries})")
                last_error = "Timeout"
            except Exception as e:
                error_type = type(e).__name__
                print(Fore.YELLOW + f"⚠️ [{ip}] Erro ao verificar Postfix: {error_type}: {str(e)[:100]}")
                last_error = f"{error_type}: {str(e)[:50]}"

        # Se chegou aqui, significa que todas as tentativas falharam em obter 'Entregue' ou 'Falha'
        print(Fore.RED + f"❌ [{ip}] Monitoramento falhou após {retries} tentativas sem obter status definitivo")
        
        if last_response:
            last_response['emailStatus'] = 'Falha - Sem resposta definitiva após tentativas'
            return last_response
        else:
            return {'status': 'failed', 'emailStatus': f"Falha após {retries} tentativas: {last_error or 'Sem resposta definitiva'}"}

async def deletar_vps(vm_code, token, proxy):
    """Deleta uma VPS específica."""
    print(Fore.YELLOW + f"🗑️ Deletando VPS {vm_code} com token {token[:5]}...")
    
    headers = {
        'Host': 'www.doprax.com',
        'User-Agent': 'Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:135.0) Gecko/******** Firefox/135.0',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'en-US,en;q=0.5',
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json',
        'Origin': 'https://www.doprax.com',
        'Connection': 'keep-alive',
        'Referer': 'https://www.doprax.com/v/virtual-machines/',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin',
        'TE': 'trailers',
    }
    
    try:
        # Criar um objeto TCPConnector com verify_ssl=False
        connector = aiohttp.TCPConnector(verify_ssl=False)

        # Modificar todas as criações de ClientSession para usar o connector
        async with aiohttp.ClientSession(connector=connector) as session:
            async with session.delete(
                f'https://www.doprax.com/api/v1/vms/{vm_code}/',
                headers=headers,
                proxy=proxy
            ) as response:
                if response.status in (200, 204, 202):
                    print(Fore.GREEN + f"✅ VPS {vm_code} deletada com sucesso!")
                    return True
                else:
                    response_text = await response.text()
                    print(Fore.RED + f"❌ Erro ao deletar VPS {vm_code}. Status: {response.status}")
                    print(Fore.RED + f"Resposta: {response_text[:200]}")
                    
                    # Verificar mensagens específicas
                    if "not found" in response_text.lower():
                        print(Fore.YELLOW + f"⚠️ VPS {vm_code} já não existe na plataforma.")
                        return True  # Consideramos como sucesso, pois a VM não existe mais
                    
                    return False
                    
    except Exception as e:
        print(Fore.RED + f"❌ Exceção ao deletar VPS {vm_code}: {type(e).__name__}: {str(e)}")
        return False


async def process_single_account(token):
    global TOKEN_PROXY_MAP, TOKENS, PROXIES
    
    proxy = get_proxy_for_token(token)
    print(Fore.CYAN + f"\n🔍 Verificando conta/token: {token[:5]}... (Proxy: {proxy.split('@')[-1]})")

    # Etapa 1: Listar VMs existentes da API Doprax
    platform_count = await check_account_vm_count(token, proxy)
    if platform_count is None:
        print(Fore.RED + f"❌ Não foi possível obter informações da conta {token[:5]}...")
        return
        
    print(Fore.CYAN + f"📊 Conta possui {platform_count}/{MAX_VPS} VMs na plataforma Doprax")
    
    # Etapa 1.5: Obter lista de IPs marcados como "Entregue" para proteção
    entregue_ips = obter_ips_entregue()
    print(Fore.GREEN + f"🛡️ Encontrados {len(entregue_ips)} IPs de produção marcados como 'Entregue' (protegidos)")
    
    # Etapa 2: Atualizar registros no banco de dados (obter credenciais atualiza o DB)
    print(Fore.CYAN + "📋 Obtendo e registrando credenciais de todas as VPS existentes...")
    credentials = await obter_credenciais(token, proxy)
    print(Fore.GREEN + f"✅ Obtidas e registradas credenciais para {len(credentials)} máquinas")

    # Etapa 3: Se estiver abaixo do limite, criar novas VMs
    if platform_count < MAX_VPS:
        vps_to_create = MAX_VPS - platform_count
        print(Fore.YELLOW + f"⚠️ O limite de VPS não foi atingido. Criando {vps_to_create} novas VPS...")

        # Criar VMs uma a uma com delay de 1 segundo
        creation_results = []
        for _ in range(vps_to_create):
            result = await criar_droplet(token, proxy)
            creation_results.append(result)
            # Adiciona delay de 1 segundo antes da próxima criação
            await asyncio.sleep(1)

        successful_creations = sum(1 for result in creation_results if result)
        print(Fore.GREEN + f"✅ {successful_creations} Droplets criadas com sucesso!")

        if successful_creations > 0:
            # Monitorar as novas VMs até que estejam prontas para configuração
            print(Fore.CYAN + "🔍 Monitorando novas VMs até que estejam prontas para configuração...")
            ready_vms = await wait_for_vm_details(token, proxy, successful_creations)
            
            if ready_vms:
                print(Fore.GREEN + f"✅ {len(ready_vms)} VMs estão prontas para configuração")
                
                # Atualizar registros no banco com novas VMs criadas
                print(Fore.CYAN + "🔄 Atualizando registros com novas VMs criadas...")
                fresh_credentials = await obter_credenciais(token, proxy)
                print(Fore.GREEN + f"✅ Base de dados atualizada com {len(fresh_credentials)} máquinas")
                
                # Configurar imediatamente as VMs que já estão prontas
                new_vm_ips = []
                for vm_code, ip, status in ready_vms:
                    # Verificar se o IP já está cadastrado no banco
                    with sqlite3.connect('unified_system.db') as conn:
                        cursor = conn.cursor()
                        cursor.execute("SELECT password FROM machines WHERE ip_address = ? AND provider_token = ?", (ip, token))
                        password_row = cursor.fetchone()
                        
                        if password_row and password_row[0]:
                            new_vm_ips.append((ip, password_row[0]))
                
                if new_vm_ips:
                    print(Fore.CYAN + f"🔧 Configurando imediatamente {len(new_vm_ips)} VMs prontas...")
                    immediate_configure_tasks = [connect_and_execute(ip, password) for ip, password in new_vm_ips]
                    immediate_results = await asyncio.gather(*immediate_configure_tasks)
                    
                    # Atualizar verificação de sucesso baseado no resultado.success
                    success_count = sum(1 for result in immediate_results if result.get('success', False))
                    print(Fore.GREEN + f"✅ {success_count}/{len(new_vm_ips)} VMs configuradas imediatamente com sucesso")
            else:
                print(Fore.YELLOW + "⚠️ Nenhuma VM atingiu o status 'pronta para configuração' no tempo de espera")
    else:
        print(Fore.YELLOW + f"⚠️ Esta conta já possui {platform_count}/{MAX_VPS} VMs na plataforma. Não é possível criar mais.")

    # Etapa 4: Configurar via SSH todas as VMs não configuradas, ignorando VMs em produção
    with sqlite3.connect('unified_system.db') as conn:
        cursor = conn.cursor()
        cursor.execute("""
            SELECT m.ip_address, m.password FROM machines m
            LEFT JOIN serversAtivosEnvio s ON s.url = 'http://' || m.ip_address
            WHERE m.provider_token = ? 
            AND (m.configurada = 0 OR m.configurada IS NULL)
            AND (s.status IS NULL OR s.status != 'Entregue')
        """, (token,))
        machines_to_configure = cursor.fetchall()
        
        # Filtrar para não incluir IPs de produção
        machines_to_configure = [(ip, password) for ip, password in machines_to_configure if ip not in entregue_ips]

    if machines_to_configure:
        print(Fore.CYAN + f"🔧 Configurando {len(machines_to_configure)} novas máquinas via SSH...")
        # Cada tarefa tem sua própria sessão
        configure_tasks = [connect_and_execute(ip, password) for ip, password in machines_to_configure]
        configure_results = await asyncio.gather(*configure_tasks)
        
        # Atualizar verificação de sucesso baseado no resultado.success
        success_count = sum(1 for result in configure_results if result.get('success', False))
        print(Fore.GREEN + f"✅ {success_count}/{len(machines_to_configure)} máquinas configuradas com sucesso")
    else:
        print(Fore.YELLOW + "ℹ️ Não há máquinas para configurar.")

    # Etapa 5: Monitoramento das VMs configuradas, ignorando VMs em produção
    print(Fore.CYAN + "🔍 Iniciando monitoramento das VMs configuradas...")
    with sqlite3.connect('unified_system.db') as conn:
        cursor = conn.cursor()
        cursor.execute("""
            SELECT m.ip_address, m.vm_code FROM machines m
            LEFT JOIN serversAtivosEnvio s ON s.url = 'http://' || m.ip_address
            WHERE m.configurada = 1 
            AND m.provider_token = ?
            AND (s.status IS NULL OR s.status != 'Entregue')
        """, (token,))
        configured_vms = cursor.fetchall()
        
        # Filtrar novamente para garantir que VMs em produção não serão monitoradas
        configured_vms = [(ip, vm_code) for ip, vm_code in configured_vms if ip not in entregue_ips]

    if configured_vms:
        print(Fore.YELLOW + f"📊 Monitorando {len(configured_vms)} máquinas configuradas (excluindo produção)...")
        # Cada tarefa monitor_postfix usa sua própria sessão internamente
        monitor_tasks = [monitor_postfix(ip, "<EMAIL>") for ip, _ in configured_vms]
        monitor_results = await asyncio.gather(*monitor_tasks)

        for (ip, vm_code), result in zip(configured_vms, monitor_results):
            nome = f"Ubuntu-{ip.split('.')[-1]}"
            email_status = result.get('emailStatus', 'Desconhecido')
            
            print(Fore.CYAN + f"📌 [{ip}] Status Postfix: {email_status}")
            
            if email_status == 'Entregue':
                print(Fore.GREEN + f"✅ [{ip}] Postfix funcionando (Entregue)")
                await sync_server_status(ip, token, 'Entregue', nome)
            elif "Email retornou" in email_status or "Falha" in email_status or 'delivery temporarily suspended' in email_status: 
                print(Fore.RED + f"❌ [{ip}] Falha crítica no Postfix. Removendo...")
                
                # Se for falha crítica, deletar VM e limpar registros
                delete_result = await deletar_vps(vm_code, token, proxy)
                if delete_result:
                    print(Fore.GREEN + f"✅ VM {ip} deletada com sucesso")
                    
                    # Limpar registros no banco de dados
                    with sqlite3.connect('unified_system.db') as conn:
                        cursor = conn.cursor()
                        cursor.execute("DELETE FROM machines WHERE ip_address = ?", (ip,))
                        cursor.execute("DELETE FROM serversAtivosEnvio WHERE url = ?", (f"http://{ip}",))
                        conn.commit()
                    print(Fore.GREEN + f"✅ Registros de {ip} removidos do banco de dados")
                else:
                    print(Fore.RED + f"❌ Falha ao deletar VM {ip}")
            else:
                # Tentar reconfigurar em caso de problemas temporários
                if "404" in str(email_status) or "connection" in str(email_status).lower():
                    print(Fore.YELLOW + f"⚠️ [{ip}] Problema temporário. Tentando reconfigurar...")
                    
                    # Obter senha atual
                    with sqlite3.connect('unified_system.db') as conn:
                        cursor = conn.cursor()
                        cursor.execute("SELECT password FROM machines WHERE ip_address = ?", (ip,))
                        password_row = cursor.fetchone()
                        
                    if password_row:
                        password = password_row[0]
                        reconfigure_result = await connect_and_execute(ip, password)
                        
                        if reconfigure_result.get('success', False):
                            print(Fore.GREEN + f"✅ [{ip}] Reconfiguração bem-sucedida")
                            
                            # Verificar status após reconfiguração
                            recheck_result = await monitor_postfix(ip, "<EMAIL>")
                            if recheck_result.get('emailStatus') == 'Entregue':
                                print(Fore.GREEN + f"✅ [{ip}] Postfix funcionando após reconfiguração")
                                await sync_server_status(ip, token, 'Entregue', nome)
                            else:
                                print(Fore.RED + f"❌ [{ip}] Postfix ainda com problemas após reconfiguração")
                                await sync_server_status(ip, token, recheck_result.get('emailStatus', 'Problema desconhecido'), nome)
                        else:
                            print(Fore.RED + f"❌ [{ip}] Falha na reconfiguração")
                    else:
                        print(Fore.RED + f"❌ [{ip}] Não foi possível obter senha para reconfiguração")
                else:
                    # Registrar o status atual
                    await sync_server_status(ip, token, email_status, nome)
    else:
        print(Fore.YELLOW + "ℹ️ Não há máquinas configuradas para monitorar (excluindo produção).")
    
    print(Fore.GREEN + f"✅ Processamento da conta {token[:5]}... concluído!")

async def sync_server_status(ip, token, status, nome=None):
    """Sincroniza o status do servidor no banco de dados."""
    nome = nome or f"Ubuntu-{ip.split('.')[-1]}"
    url = f"http://{ip}"
    
    with sqlite3.connect('unified_system.db') as conn:
        cursor = conn.cursor()
        
        # Verificar se o servidor existe e seu status atual
        cursor.execute("SELECT status FROM serversAtivosEnvio WHERE url = ? OR url = ?", (url, ip))
        result = cursor.fetchone()
        current_status = result[0] if result else None
        exists = result is not None
        
        # Se estava "Entregue" e agora está com problemas temporários, mantenha-o no banco
        if current_status == 'Entregue' and status.startswith('Problema'):
            cursor.execute("""
                UPDATE serversAtivosEnvio 
                SET status = ?, token = ?, nome = ?, ativo = 0
                WHERE url = ? OR url = ?
            """, (status, token, nome, url, ip))
            print(Fore.YELLOW + f"⚠️ [{ip}] Marcado como '{status}' (temporariamente inativo, mas preservado)")
            conn.commit()
            return
            
        if status == 'Entregue':
            if exists:
                cursor.execute("""
                    UPDATE serversAtivosEnvio 
                    SET status = ?, token = ?, nome = ?, ativo = 1
                    WHERE url = ? OR url = ?
                """, (status, token, nome, url, ip))
            else:
                cursor.execute("""
                    INSERT INTO serversAtivosEnvio (nome, url, ativo, status, token)
                    VALUES (?, ?, 1, ?, ?)
                """, (nome, url, status, token))
            print(Fore.GREEN + f"✅ [{ip}] Atualizado no banco como '{status}'")
        else:
            if exists:
                # Não delete servidores que já foram "Entregue"
                if (status == 'Falha - Email retornou' or status.startswith('Falha')) and current_status != 'Entregue':
                    cursor.execute("DELETE FROM serversAtivosEnvio WHERE url = ? OR url = ?", (url, ip))
                    print(Fore.YELLOW + f"🗑️ [{ip}] Removido do banco (status: {status})")
                else:
                    cursor.execute("""
                        UPDATE serversAtivosEnvio 
                        SET status = ?, token = ?, nome = ?
                        WHERE url = ? OR url = ?
                    """, (status, token, nome, url, ip))
                    print(Fore.YELLOW + f"⚠️ [{ip}] Status atualizado para '{status}'")
        
        conn.commit()

async def check_account_vm_count(token, proxy):
    """Verifica quantas VMs estão ativas para um token específico."""
    global TOKEN_PROXY_MAP, TOKENS, PROXIES
    
    headers = {
        'Host': 'www.doprax.com',
        'user-agent': 'Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:135.0) Gecko/******** Firefox/135.0',
        'accept': 'application/json, text/plain, */*',
        'accept-language': 'en-US,en;q=0.5',
        'referer': 'https://www.doprax.com/v/me/',
        'authorization': f'Bearer {token}',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-origin',
        'pragma': 'no-cache',
        'cache-control': 'no-cache',
    }

    try:
        # Criar um objeto TCPConnector com verify_ssl=False
        connector = aiohttp.TCPConnector(verify_ssl=False)

        # Modificar todas as criações de ClientSession para usar o connector
        async with aiohttp.ClientSession(connector=connector) as session:
            async with session.get('https://www.doprax.com/api/v1/vms/', headers=headers, proxy=proxy) as response:
                if response.status == 403:
                    print(Fore.RED + f"❌ Token {token[:5]}... retornou 403 (Proibido). Removendo este token do arquivo.")
                    await remove_token_from_file(token)
                    # Atualizar o mapeamento em memória
                    if token in TOKEN_PROXY_MAP:
                        del TOKEN_PROXY_MAP[token]
                        TOKENS = list(TOKEN_PROXY_MAP.keys())
                        PROXIES = list(TOKEN_PROXY_MAP.values())
                    return None
                
                # Verificar mensagem de crédito insuficiente
                response_text = await response.text()
                if "You don't have enough credit" in response_text or "Insufficient credit" in response_text:
                    print(Fore.RED + f"❌ Token {token[:5]}... não tem crédito suficiente. Removendo este token do arquivo.")
                    await remove_token_from_file(token)
                    # Remover todas as VMs associadas a este token
                    await remove_all_vms_for_token(token, proxy)
                    # Atualizar o mapeamento em memória
                    if token in TOKEN_PROXY_MAP:
                        del TOKEN_PROXY_MAP[token]
                        TOKENS = list(TOKEN_PROXY_MAP.keys())
                        PROXIES = list(TOKEN_PROXY_MAP.values())
                    return None
                
                if response.status == 200:
                    try:
                        data = await response.json()
                        vm_list = data.get("vm_list", [])
                        print(Fore.CYAN + f"📊 Conta possui {len(vm_list)} VMs na plataforma Doprax")
                        return len(vm_list)
                    except json.JSONDecodeError:
                        print(Fore.RED + f"❌ Erro ao decodificar resposta JSON: {response_text[:100]}...")
                        return None
                else:
                    print(Fore.RED + f"❌ Erro ao verificar quantidade de VMs: {response.status}")
                    return None
    except Exception as e:
        print(Fore.RED + f"❌ Exceção ao verificar VMs: {str(e)}")
        return None

async def wait_for_vm_details(token, proxy, expected_count, max_wait_time=900):
    """
    Espera até que as novas VMs estejam prontas para configuração, monitorando os status de forma ativa.
    Verifica cada nova VM individualmente para acompanhar a transição de status.
    
    Args:
        token: Token de autenticação da API
        proxy: Proxy para requisições HTTP
        expected_count: Número esperado de VMs para monitorar
        max_wait_time: Tempo máximo de espera em segundos (15 minutos por padrão)
    
    Returns:
        Uma lista de tuplas (vm_code, ip, status) das VMs que atingiram o status 'active'/'running'
    """
    global TOKEN_PROXY_MAP, TOKENS, PROXIES
    
    print(Fore.CYAN + f"🔍 Iniciando monitoramento de {expected_count} novas VMs...")
    
    headers = {
        'Host': 'www.doprax.com',
        'user-agent': 'Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:135.0) Gecko/******** Firefox/135.0',
        'accept': 'application/json, text/plain, */*',
        'authorization': f'Bearer {token}',
    }

    # Primeiro, obter a lista de novas VMs
    vm_list = []
    try:
        # Criar um objeto TCPConnector com verify_ssl=False
        connector = aiohttp.TCPConnector(verify_ssl=False)

        # Modificar todas as criações de ClientSession para usar o connector
        async with aiohttp.ClientSession(connector=connector) as session:
            async with session.get('https://www.doprax.com/api/v1/vms/', headers=headers, proxy=proxy) as response:
                if response.status == 403:
                    print(Fore.RED + f"❌ Token {token[:5]}... retornou 403 (Proibido). Removendo este token do arquivo.")
                    await remove_token_from_file(token)
                    # Atualizar o mapeamento em memória
                    if token in TOKEN_PROXY_MAP:
                        # Remover todas as VMs associadas a este token
                        await remove_all_vms_for_token(token, proxy)
                        # Remover token do mapeamento em memória
                        del TOKEN_PROXY_MAP[token]
                        TOKENS = list(TOKEN_PROXY_MAP.keys())
                        PROXIES = list(TOKEN_PROXY_MAP.values())
                    return []
                
                # Verificar mensagem de crédito insuficiente
                response_text = await response.text()
                if "You don't have enough credit" in response_text or "Insufficient credit" in response_text:
                    print(Fore.RED + f"❌ Token {token[:5]}... não tem crédito suficiente. Removendo este token do arquivo.")
                    await remove_token_from_file(token)
                    # Remover todas as VMs associadas a este token
                    await remove_all_vms_for_token(token, proxy)
                    # Atualizar o mapeamento em memória
                    if token in TOKEN_PROXY_MAP:
                        del TOKEN_PROXY_MAP[token]
                        TOKENS = list(TOKEN_PROXY_MAP.keys())
                        PROXIES = list(TOKEN_PROXY_MAP.values())
                    return []
                    
                if response.status != 200:
                    print(Fore.RED + f"❌ Falha ao obter lista de VMs. Status: {response.status}")
                    return []
                
                try:
                    data = await response.json()
                    vm_list = data.get("vm_list", [])
                except json.JSONDecodeError:
                    print(Fore.RED + f"❌ Erro ao decodificar resposta JSON: {response_text[:100]}...")
                    return []
    except Exception as e:
        print(Fore.RED + f"❌ Erro ao obter lista de VMs: {type(e).__name__}: {str(e)}")
        return []

    # Filtrar VMs recém-criadas (status "new" ou "almost_ready")
    new_vms = [
        vm for vm in vm_list 
        if vm.get("status") in ["new", "almost_ready"] or not vm.get("is_active", True)
    ][:expected_count]
    
    if not new_vms:
        print(Fore.YELLOW + "⚠️ Nenhuma VM nova encontrada para monitorar")
        return []
    
    print(Fore.GREEN + f"✅ Encontradas {len(new_vms)} novas VMs para monitorar")
    
    # Monitorar cada VM até que atinja o status 'active' ou 'running'
    ready_vms = []
    monitoring_vms = {vm.get('vm_code'): vm for vm in new_vms}
    
    start_time = datetime.now()
    check_interval = 300  #Verificação a cada 30 segundos
    
    while monitoring_vms and (datetime.now() - start_time).total_seconds() < max_wait_time:
        print(Fore.CYAN + f"🔄 Verificando status de {len(monitoring_vms)} VMs...")
        
        vm_codes_to_remove = []
        
        # Criar uma nova sessão para cada ciclo de verificação
        async with aiohttp.ClientSession() as fresh_session:
            for vm_code, vm_data in monitoring_vms.items():
                try:
                    # Verificar status individual da VM
                    async with fresh_session.get(
                        f'https://www.doprax.com/api/v1/vms/{vm_code}/', 
                        headers=headers, 
                        proxy=proxy
                    ) as vm_response:
                        await asyncio.sleep(0.4)
                        if vm_response.status != 200:
                            print(Fore.YELLOW + f"⚠️ Falha ao verificar VM {vm_code}. Status: {vm_response.status}")
                            continue
                        
                        vm_detail = await vm_response.json()
                        vm_data = vm_detail.get("vmData", {}) or vm_detail.get("data", {})
                        
                        current_status = vm_data.get("status", "").lower()
                        ip = vm_data.get("ipv4")
                        is_active = vm_data.get("is_active", False) or vm_data.get("isActive", False)
                        
                        print(Fore.CYAN + f"📌 VM {vm_code[:8]}... - IP: {ip or 'None'} - Status: {current_status} - Ativo: {is_active}")
                        
                        # Verificar se está pronta para configuração
                        if (current_status in ["active", "running"] or is_active) and ip:
                            print(Fore.GREEN + f"✅ VM {vm_code[:8]}... pronta para configuração! IP: {ip}")
                            ready_vms.append((vm_code, ip, current_status))
                            vm_codes_to_remove.append(vm_code)
                        elif current_status == "almost_ready" and ip:
                            print(Fore.YELLOW + f"⏳ VM {vm_code[:8]}... quase pronta. IP atribuído: {ip}")
                        elif not ip:
                            print(Fore.YELLOW + f"⏳ VM {vm_code[:8]}... aguardando atribuição de IP...")
                except Exception as e:
                    print(Fore.RED + f"❌ Erro ao verificar VM {vm_code}: {type(e).__name__}: {str(e)}")
        
        # Remover VMs prontas da lista de monitoramento
        for vm_code in vm_codes_to_remove:
            monitoring_vms.pop(vm_code, None)
        
        if monitoring_vms:
            elapsed = (datetime.now() - start_time).total_seconds()
            remaining = max_wait_time - elapsed
            
            if remaining <= 0:
                print(Fore.RED + f"⏱️ Tempo máximo de espera atingido ({max_wait_time}s)")
                break
                
            print(Fore.YELLOW + f"⏳ Aguardando {check_interval}s antes da próxima verificação... (Restante: {int(remaining)}s)")
            await asyncio.sleep(check_interval)
    
    time_spent = (datetime.now() - start_time).total_seconds()
    print(Fore.GREEN + f"✅ Monitoramento concluído em {time_spent:.1f}s. {len(ready_vms)}/{expected_count} VMs prontas.")
    
    # Se ainda houver VMs pendentes, exibir alerta
    if monitoring_vms:
        print(Fore.YELLOW + f"⚠️ {len(monitoring_vms)} VMs não ficaram prontas no tempo limite")
        
    return ready_vms

async def create_max_vps_all_accounts():
    """Create the maximum number of VPS in all accounts simultaneously."""
    global TOKEN_PROXY_MAP, TOKENS, PROXIES
    
    print(Fore.CYAN + "🔍 Criando o máximo de VPS em todas as contas simultaneamente...")
    # Recarregar tokens e proxies antes de executar
    TOKEN_PROXY_MAP = load_token_proxy_map()
    TOKENS = list(TOKEN_PROXY_MAP.keys())
    PROXIES = list(TOKEN_PROXY_MAP.values())
    
    # Criar um semáforo para limitar o número de requisições concorrentes
    semaphore = asyncio.Semaphore(100)
    
    async def create_vps_with_semaphore(token):
        """Criar uma VPS com controle de concorrência"""
        async with semaphore:
            proxy = get_proxy_for_token(token)
            vps_config = random.choice(VPS_CONFIGURATIONS)
            print(Fore.CYAN + f"🌍 Tentando criar VPS em {vps_config['location_country']} usando {vps_config['provider_name']} ({vps_config['vm_type']}) - Token: {token[:5]}...")
            
            headers = {
                'Host': 'www.doprax.com',
                'user-agent': 'Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:135.0) Gecko/******** Firefox/135.0',
                'accept': 'application/json, text/plain, */*',
                'accept-language': 'en-US,en;q=0.5',
                'referer': 'https://www.doprax.com/v/virtual-machines/add-new/',
                'content-type': 'application/json',
                'authorization': f'Bearer {token}',
                'origin': 'https://www.doprax.com',
                'sec-fetch-dest': 'empty',
                'sec-fetch-mode': 'cors',
                'sec-fetch-site': 'same-origin',
            }

            try:
                connector = aiohttp.TCPConnector(verify_ssl=False)
                async with aiohttp.ClientSession(connector=connector) as session:
                    async with session.post(
                        'https://www.doprax.com/api/v1/vms/',
                        headers=headers,
                        json=vps_config,
                        proxy=proxy
                    ) as response:
                        response_text = await response.text()
                        
                        if response.status == 201:
                            print(Fore.GREEN + f"✅ VPS criada com sucesso em {vps_config['location_country']} - Token: {token[:5]}")
                            return True, token, vps_config['location_country']
                        elif "You can only create 10 VMs" in response_text:
                            print(Fore.YELLOW + f"⚠️ Limite de 10 VMs atingido - Token: {token[:5]}")
                            return False, token, "Limite atingido"
                        elif "You don't have enough credit" in response_text:
                            print(Fore.RED + f"❌ Crédito insuficiente - Token: {token[:5]}")
                            await remove_token_from_file(token)
                            await remove_all_vms_for_token(token, proxy)
                            return False, token, "Sem crédito"
                        else:
                            print(Fore.RED + f"❌ Erro ao criar VPS - Token: {token[:5]} - Status: {response.status}")
                            return False, token, f"Erro {response.status}"
            except Exception as e:
                print(Fore.RED + f"❌ Exceção ao criar VPS - Token: {token[:5]}: {str(e)}")
                return False, token, str(e)

    # Criar lista de tarefas para cada token, multiplicado por MAX_VPS
    creation_tasks = []
    for token in TOKEN_PROXY_MAP:
        # Tentar criar MAX_VPS para cada token simultaneamente
        token_tasks = [create_vps_with_semaphore(token) for _ in range(MAX_VPS)]
        creation_tasks.extend(token_tasks)
    
    print(Fore.CYAN + f"🚀 Iniciando {len(creation_tasks)} tentativas de criação de VPS simultaneamente...")
    
    # Executar todas as tarefas simultaneamente
    results = await asyncio.gather(*creation_tasks)
    
    # Processar resultados
    successful_creations = sum(1 for success, _, _ in results if success)
    total_attempts = len(results)
    
    # Agrupar resultados por token
    results_by_token = {}
    for success, token, status in results:
        if token not in results_by_token:
            results_by_token[token] = {'success': 0, 'failures': []}
        if success:
            results_by_token[token]['success'] += 1
        else:
            results_by_token[token]['failures'].append(status)
    
    # Exibir resumo
    print(Fore.CYAN + "\n📊 Resumo da criação de VPS:")
    print(Fore.GREEN + f"✅ Total de VPS criadas com sucesso: {successful_creations}/{total_attempts}")
    
    for token, result in results_by_token.items():
        success_count = result['success']
        failure_reasons = result['failures']
        print(Fore.CYAN + f"\nToken {token[:5]}:")
        print(Fore.GREEN + f"  ✅ Sucesso: {success_count}")
        if failure_reasons:
            print(Fore.YELLOW + f"  ⚠️ Falhas: {len(failure_reasons)}")
            failure_counts = {}
            for reason in failure_reasons:
                failure_counts[reason] = failure_counts.get(reason, 0) + 1
            for reason, count in failure_counts.items():
                print(Fore.RED + f"    - {reason}: {count}x")
    
    print(Fore.GREEN + "\n✅ Processo de criação de VPS concluído!")

async def configure_all_vps():
    """Configure all VPS that are not yet configured."""
    print(Fore.CYAN + "🔧 Configurando todas as VPS não configuradas...")
    with sqlite3.connect('unified_system.db') as conn:
        cursor = conn.cursor()
        cursor.execute("""
            SELECT ip_address, password FROM machines 
            WHERE configurada = 0
        """)
        machines_to_configure = cursor.fetchall()

    if machines_to_configure:
        configure_tasks = [connect_and_execute(ip, password) for ip, password in machines_to_configure]
        configure_results = await asyncio.gather(*configure_tasks)
        # Atualizar verificação de sucesso baseado no resultado.success
        success_count = sum(1 for result in configure_results if result.get('success', False))
        print(Fore.GREEN + f"✅ {success_count}/{len(machines_to_configure)} máquinas configuradas com sucesso")
    else:
        print(Fore.YELLOW + "ℹ️ Não há máquinas para configurar.")

async def delete_all_vps():
    """Delete all VPS across all accounts by fetching the list from the site."""
    global TOKEN_PROXY_MAP, TOKENS, PROXIES
    
    print(Fore.RED + "🗑️ Deletando todas as VPS em todas as contas a partir do site...")

    # Recarregar tokens e proxies antes de executar
    TOKEN_PROXY_MAP = load_token_proxy_map()
    TOKENS = list(TOKEN_PROXY_MAP.keys())
    PROXIES = list(TOKEN_PROXY_MAP.values())
    
    for token in TOKEN_PROXY_MAP:
        proxy = get_proxy_for_token(token)
        headers = {
            'Host': 'www.doprax.com',
            'user-agent': 'Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:135.0) Gecko/******** Firefox/135.0',
            'accept': 'application/json, text/plain, */*',
            'authorization': f'Bearer {token}',
        }
        connector = aiohttp.TCPConnector(verify_ssl=False)
        async with aiohttp.ClientSession(connector=connector) as session:
            async with session.get('https://www.doprax.com/api/v1/vms/', headers=headers, proxy=proxy) as response:
                if response.status == 200:
                    data = await response.json()
                    vm_list = data.get("vm_list", [])
                    delete_tasks = [
                        deletar_vps(vm['vm_code'], token, proxy) for vm in vm_list
                    ]
                    await asyncio.gather(*delete_tasks)
                    print(Fore.GREEN + f"✅ Todas as VPS para o token {token[:5]} foram deletadas do site.")
                else:
                    print(Fore.RED + f"❌ Erro ao obter lista de VPS para o token {token[:5]}. Status: {response.status}")

    # Limpar o banco de dados após deletar as VPS do site
    with sqlite3.connect('unified_system.db') as conn:
        cursor = conn.cursor()
        cursor.execute("DELETE FROM machines")
        cursor.execute("DELETE FROM serversAtivosEnvio")
        conn.commit()
    print(Fore.GREEN + "✅ Todas as VPS foram removidas do banco de dados.")

async def main_menu():
    global TOKEN_PROXY_MAP, TOKENS, PROXIES
    
    while True:
        print("\nMenu:")
        print("1. Executar Fluxo Completo para Todas as Contas (Mais Robusto)")
        print("2. Verificar Status do Postfix em Todas as VPS Ativas")
        print("3. Criar Máximo de VPS em Todas as Contas")
        print("4. Configurar Todas as VPS")
        print("5. Deletar Todas as VPS")
        print("6. Recarregar Tokens e Proxies do Arquivo")
        print("7. Testar Deploy Docker em Servidor Específico")
        print("8. Sair")

        choice = input("Escolha uma opção: ")

        if choice == "1":
            print(Fore.CYAN + "\n🚀 Iniciando fluxo completo e robusto para todas as contas...")
            # Carregar configurações primeiro
            print(Fore.CYAN + "⚙️ Carregando configurações do banco de dados...")
            initialize_database()  # Garantir que o banco está inicializado
            
            # Recarregar tokens e proxies antes de executar
            TOKEN_PROXY_MAP = load_token_proxy_map()
            TOKENS = list(TOKEN_PROXY_MAP.keys())
            PROXIES = list(TOKEN_PROXY_MAP.values())
            
            # Processar cada conta em paralelo
            tasks = [process_single_account(token) for token in TOKEN_PROXY_MAP]
            await asyncio.gather(*tasks)
            print(Fore.GREEN + "✅ Fluxo completo e robusto executado para todas as contas!")
        
        elif choice == "2":
            # Recarregar tokens e proxies antes de executar
            TOKEN_PROXY_MAP = load_token_proxy_map()
            TOKENS = list(TOKEN_PROXY_MAP.keys())
            PROXIES = list(TOKEN_PROXY_MAP.values())
            
            await verificar_status_postfix_todas_vps()
        
        elif choice == "3":
            # Recarregar tokens e proxies antes de executar
            TOKEN_PROXY_MAP = load_token_proxy_map()
            TOKENS = list(TOKEN_PROXY_MAP.keys())
            PROXIES = list(TOKEN_PROXY_MAP.values())
            
            await create_max_vps_all_accounts()
        
        elif choice == "4":
            await configure_all_vps()
        
        elif choice == "5":
            # Recarregar tokens e proxies antes de executar
            TOKEN_PROXY_MAP = load_token_proxy_map()
            TOKENS = list(TOKEN_PROXY_MAP.keys())
            PROXIES = list(TOKEN_PROXY_MAP.values())
            
            await delete_all_vps()
        
        elif choice == "6":
            # Recarregar tokens e proxies do arquivo
            TOKEN_PROXY_MAP = load_token_proxy_map()
            TOKENS = list(TOKEN_PROXY_MAP.keys())
            PROXIES = list(TOKEN_PROXY_MAP.values())
            print(Fore.GREEN + f"✅ {len(TOKEN_PROXY_MAP)} tokens carregados/atualizados com sucesso!")
        
        elif choice == "7":
            print(Fore.CYAN + "\n=== Testar Deploy Docker em Servidor Específico ===")
            ip = input(Fore.WHITE + "IP do servidor: " + Fore.GREEN)
            password = input(Fore.WHITE + "Senha SSH: " + Fore.GREEN)
            
            # Informa que a URL fixa será usada
            print(Fore.CYAN + f"Usando URL fixa para download da imagem: http://*************/dopraxconfigurator-552289200?apikey=c4085f0de4aca21e6")
            
            asyncio.run(async_main_test_docker(ip, password))
            input(Fore.WHITE + "\nPressione Enter para continuar..." + Fore.RESET)
        
        elif choice == "8":
            print("Saindo...")
            break
            
        else:
            print("Opção inválida! Tente novamente.")

async def verificar_status_postfix_todas_vps():
    """Verifica o status do Postfix em todas as VPS ativas, INCLUINDO as marcadas como 'Entregue'."""
    global TOKEN_PROXY_MAP, TOKENS, PROXIES
    
    print(Fore.CYAN + "🔍 Verificando status do Postfix em TODAS as VPS ativas (incluindo produção)...")
    
    # Obter lista de IPs marcados como "Entregue" para informação
    entregue_ips = obter_ips_entregue()
    print(Fore.YELLOW + f"⚠️ ATENÇÃO: {len(entregue_ips)} IPs de produção serão verificados e poderão ser deletados!")
    
    # Buscar todas as máquinas ativas no banco de dados, incluindo as em produção
    with sqlite3.connect('unified_system.db') as conn:
        cursor = conn.cursor()
        
        # Consultar machines e join com serversAtivosEnvio para obter status atual
        # Incluindo explicitamente VMs marcadas como 'Entregue'
        cursor.execute("""
            SELECT m.ip_address, m.vm_code, m.provider_token, 
                   s.status, m.password, s.nome
            FROM machines m
            LEFT JOIN serversAtivosEnvio s ON s.url = 'http://' || m.ip_address
            WHERE m.active = 1
            ORDER BY 
                CASE WHEN s.status = 'Entregue' THEN 1
                     WHEN s.status IS NULL THEN 3
                     ELSE 2 END,
                m.created_at DESC
        """)
        machines = cursor.fetchall()
    
    if not machines:
        print(Fore.YELLOW + "⚠️ Não há máquinas ativas para verificação.")
        return
    
    print(Fore.GREEN + f"✅ Encontradas {len(machines)} máquinas ativas para verificação.")
    
    # Identificar e exibir quantas máquinas em produção serão verificadas
    production_machines = [(ip, vm_code) for ip, vm_code, _, status, _, _ in machines if status == 'Entregue']
    if production_machines:
        print(Fore.RED + f"⚠️ ATENÇÃO: {len(production_machines)} máquinas em produção serão verificadas!")
        for ip, _ in production_machines:
            print(Fore.RED + f"   ⚠️ Máquina em produção que será verificada: {ip}")
    
    # Estatísticas para resumo
    total = len(machines)
    success_count = 0
    problem_count = 0
    test_email = "<EMAIL>"  # Email para teste
    
    # Verificar status em paralelo com limite de concorrência
    semaphore = asyncio.Semaphore(5)  # Máximo de 5 verificações simultâneas
    
    async def verify_with_semaphore(ip, vm_code, token, current_status, password, nome):
        async with semaphore:
            try:
                is_production = current_status == 'Entregue'
                status_info = "PRODUÇÃO" if is_production else current_status or 'Desconhecido'
                print(Fore.CYAN + f"🔍 Verificando Postfix em {ip} (Status atual: {status_info})...")
                
                # Usar função monitor_postfix existente - esta função cria sua própria sessão
                result = await monitor_postfix(ip, test_email, retries=2, max_wait_time=60)
                
                # Processar resultado
                if result.get('emailStatus') == 'Entregue':
                    print(Fore.GREEN + f"✅ [{ip}] Postfix funcionando corretamente: Email entregue")
                    nome = nome or f"Ubuntu-{ip.split('.')[-1]}"
                    await sync_server_status(ip, token, 'Entregue', nome)
                    return True, ip, "Entregue", False, is_production
                else:
                    # Verificar se há erro específico
                    error_detail = result.get('message', 'Erro desconhecido')
                    error_status = result.get('emailStatus', '')
                    
                    # Mensagem de erro completa para log
                    full_error = error_status if error_status else error_detail
                    
                    # Verificar se é um dos casos que requer deleção imediata
                    should_delete = (
                        'delivery temporarily suspended' in str(full_error).lower() or
                        error_status == 'Falha - Sem resposta definitiva após tentativas' or
                        'Email retornou' in str(full_error) or
                        'Falha' in str(full_error)
                    )
                    
                    if should_delete:
                        print(Fore.RED + f"❌ [{ip}] Erro crítico detectado: {full_error}")
                        # Deletar a VM imediatamente
                        proxy = get_proxy_for_token(token)
                        try:
                            delete_result = await deletar_vps(vm_code, token, proxy)
                            if delete_result:
                                print(Fore.GREEN + f"✅ VM {ip} deletada com sucesso")
                                # Limpar registros no banco de dados
                                with sqlite3.connect('unified_system.db') as conn:
                                    cursor = conn.cursor()
                                    cursor.execute("DELETE FROM machines WHERE ip_address = ?", (ip,))
                                    cursor.execute("DELETE FROM serversAtivosEnvio WHERE url = ?", (f"http://{ip}",))
                                    conn.commit()
                                print(Fore.GREEN + f"✅ Registros de {ip} removidos do banco de dados")
                            else:
                                print(Fore.RED + f"❌ Falha ao deletar VM {ip}")
                        except Exception as e:
                            print(Fore.RED + f"❌ Erro ao deletar VM {ip}: {str(e)}")
                    
                    if is_production:
                        print(Fore.RED + f"❌ [{ip}] MÁQUINA EM PRODUÇÃO com falha no Postfix: {full_error}")
                    else:
                        print(Fore.RED + f"❌ [{ip}] Falha no Postfix: {full_error}")
                    
                    await sync_server_status(ip, token, f'Falha - {error_status or error_detail}', nome)
                    return False, ip, error_status or error_detail, should_delete, is_production
                        
            except Exception as e:
                is_production = current_status == 'Entregue'
                print(Fore.RED + f"❌ Exceção ao verificar {ip}: {type(e).__name__}: {str(e)}")
                return False, ip, str(e), True, is_production
                
    # Criar e executar tarefas de verificação
    verify_tasks = [
        verify_with_semaphore(ip, vm_code, token, status, password, nome) 
        for ip, vm_code, token, status, password, nome in machines
    ]
    results = await asyncio.gather(*verify_tasks)
    
    # Processar resultados
    success_count = sum(1 for result in results if result[0])
    
    # Identificar máquinas com erro para potencial deleção
    machines_with_errors = []
    for i, (success, ip, error_msg, needs_recheck, is_production) in enumerate(results):
        if not success:
            ip, vm_code, token, status, password, nome = machines[i]
            machines_with_errors.append((ip, vm_code, token, error_msg, is_production))
    
    # Deletar máquinas com falha no Postfix
    to_delete = []
    production_to_delete = []
    
    for ip, vm_code, token, error_msg, is_production in machines_with_errors:
        proxy = get_proxy_for_token(token)
        if is_production:
            production_to_delete.append((ip, vm_code, token, proxy))
        else:
            to_delete.append((ip, vm_code, token, proxy))
    
    # Perguntar ao usuário antes de deletar máquinas em produção
    if production_to_delete:
        print(Fore.RED + f"\n⚠️ ATENÇÃO! {len(production_to_delete)} máquinas EM PRODUÇÃO estão com problemas no Postfix:")
        for ip, _, _, _ in production_to_delete:
            print(Fore.RED + f"   ⚠️ {ip}")
        
        print(Fore.RED + "\n⚠️ DELETAR ESTAS MÁQUINAS PODE CAUSAR INTERRUPÇÃO DE SERVIÇO EM PRODUÇÃO!")
        confirm = input(Fore.RED + "Deseja realmente deletar estas máquinas em produção? (S/N): ").strip().upper()
        
        if confirm == 'S':
            print(Fore.RED + f"\n🗑️ Deletando {len(production_to_delete)} máquinas EM PRODUÇÃO...")
            to_delete.extend(production_to_delete)
        else:
            print(Fore.YELLOW + "Deleção de máquinas em produção cancelada pelo usuário.")
    
    # Executar deleções
    if to_delete:
        print(Fore.RED + f"\n🗑️ Deletando {len(to_delete)} máquinas com falha no Postfix...")
        
        for ip, vm_code, token, proxy in to_delete:
            is_production = ip in entregue_ips
            prod_marker = "[PRODUÇÃO] " if is_production else ""
            print(Fore.YELLOW + f"🗑️ Deletando VM {prod_marker}{ip} (Código: {vm_code})...")
            
            try:
                delete_result = await deletar_vps(vm_code, token, proxy)
                
                if delete_result:
                    print(Fore.GREEN + f"✅ VM {prod_marker}{ip} deletada com sucesso da plataforma")
                    
                    # Remover do banco de dados
                    with sqlite3.connect('unified_system.db') as conn:
                        cursor = conn.cursor()
                        cursor.execute("DELETE FROM machines WHERE ip_address = ?", (ip,))
                        cursor.execute("DELETE FROM serversAtivosEnvio WHERE url = ?", (f"http://{ip}",))
                        conn.commit()
                    print(Fore.GREEN + f"✅ VM {prod_marker}{ip} removida do banco de dados")
                else:
                    print(Fore.RED + f"❌ Falha ao deletar VM {prod_marker}{ip}")
            except Exception as e:
                print(Fore.RED + f"❌ Erro ao deletar VM {prod_marker}{ip}: {str(e)}")
    
    # Calcular estatísticas finais
    problem_count = total - success_count
    
    # Exibir resumo
    print(Fore.CYAN + "\n📊 Resumo da verificação de Postfix:")
    percent_success = (success_count / total * 100) if total > 0 else 0
    print(Fore.GREEN + f"   ✅ {success_count}/{total} ({percent_success:.1f}%) máquinas com Postfix funcionando")
    
    if problem_count > 0:
        percent_problem = (problem_count / total * 100) if total > 0 else 0
        print(Fore.RED + f"   ❌ {problem_count}/{total} ({percent_problem:.1f}%) máquinas com problemas no Postfix")
        print(Fore.RED + f"   🗑️ {len(to_delete)} máquinas foram deletadas devido a falhas")
    
    print(Fore.GREEN + "\n✅ Verificação de Postfix concluída!")

async def loop_opcoes_1_e_2():
    """
    Fluxo automatizado e contínuo para gerenciamento de VMs:
    1. Identifica e protege VMs de produção (marcadas como 'Entregue')
    2. Executa o fluxo completo e robusto para todas as contas
    3. Aguarda um intervalo antes de executar novamente
    """
    global TOKEN_PROXY_MAP, TOKENS, PROXIES
    
    print(Fore.CYAN + "🔄 Iniciando fluxo automatizado e robusto de gerenciamento de VMs")
    
    # Loop principal
    while True:
        try:
            # Carregar configurações do banco de dados
            print(Fore.CYAN + "\n⚙️ CARREGANDO CONFIGURAÇÕES DO BANCO DE DADOS")
            initialize_database()  # Garantir que o banco está inicializado
            
            # Recarregar tokens e proxies antes de executar
            TOKEN_PROXY_MAP = load_token_proxy_map()
            TOKENS = list(TOKEN_PROXY_MAP.keys())
            PROXIES = list(TOKEN_PROXY_MAP.values())
            
            # Identificar VMs de produção para proteção
            entregue_ips = obter_ips_entregue()
            print(Fore.GREEN + f"\n🛡️ SISTEMA DE PROTEÇÃO: {len(entregue_ips)} VMs DE PRODUÇÃO IDENTIFICADAS")
            for ip in entregue_ips:
                print(Fore.GREEN + f"  ✅ VM protegida: {ip}")
            
            # Executar fluxo completo e robusto para todas as contas
            print(Fore.CYAN + "\n🚀 EXECUTANDO FLUXO COMPLETO E ROBUSTO PARA TODAS AS CONTAS")
            print(Fore.YELLOW + "⚠️ AVISO: VMs marcadas como 'Entregue' não serão manipuladas")
            
            tasks = [process_single_account(token) for token in TOKEN_PROXY_MAP]
            await asyncio.gather(*tasks)
            print(Fore.GREEN + "✅ Fluxo completo e robusto executado para todas as contas!")
            
            # Esperar um intervalo antes do próximo ciclo
            print(Fore.YELLOW + "\n⏳ Aguardando 10 minutos antes do próximo ciclo...")
            await asyncio.sleep(300)  # 10 minutos
            
        except Exception as e:
            print(Fore.RED + f"❌ Erro no ciclo de automação: {type(e).__name__}: {str(e)}")
            print(Fore.YELLOW + "⏳ Aguardando 2 minutos antes de tentar novamente...")
            await asyncio.sleep(120)  # 2 minutos de espera após erro

# Adicionar uma função para testar o deploy Docker
async def async_main_test_docker(ip, password, server_url=None):
    print(Fore.CYAN + f"\n🚀 Iniciando teste de deploy Docker em {ip}...")
    
    try:
        result = await connect_and_execute(ip, password)
        
        # Atualizar verificação de sucesso baseado na nova mensagem
        if "✅ SERVICE_VERIFICATION: ✅ Serviço está rodando corretamente" in str(result):
            print(Fore.GREEN + f"✅ Deploy Docker concluído com sucesso em {ip}")
            print(Fore.CYAN + "Status: Serviço rodando corretamente")
        else:
            print(Fore.RED + f"❌ Falha no deploy Docker em {ip}")
            print(Fore.RED + f"Detalhes do resultado: {result}")
    except Exception as e:
        print(Fore.RED + f"❌ Erro ao executar deploy Docker: {str(e)}")
        traceback.print_exc()

if __name__ == "__main__":
    # Inicializar banco de dados
    initialize_database()
    
    # Verificar se o arquivo de tokens existe e criar se não existir
    if not os.path.exists(TOKEN_PROXY_FILE):
        print(Fore.YELLOW + f"⚠️ Arquivo {TOKEN_PROXY_FILE} não encontrado. Criando arquivo modelo.")
        with open(TOKEN_PROXY_FILE, 'w') as f:
            f.write("# Formato: token|proxy\n")
            f.write("# Exemplo: TStk10Irm1CnPkiY3Xkwlu2MGqQW0FgQHLWw1UAyBs5tkcSvZlFhf5yLbhlKZG0MJdhGpLgsZvVuMiQiSDalrIRgHtbi4IjgkQwt|***************************:port\n")
            f.write("# Adicione seus tokens e proxies abaixo, um por linha\n")
            f.write("# Tokens inválidos (403) serão automaticamente removidos\n\n")
            f.write("# TStk10Irm1CnPkiY3Xkwlu2MGqQW0FgQHLWw1UAyBs5tkcSvZlFhf5yLbhlKZG0MJdhGpLgsZvVuMiQiSDalrIRgHtbi4IjgkQwt|http://matttunner:DtmIxzoki8@************:50100\n")
        print(Fore.YELLOW + f"⚠️ Arquivo modelo criado. Por favor, edite {TOKEN_PROXY_FILE} e adicione seus tokens e proxies antes de continuar.")
    
    # Carregar tokens antes de iniciar
    if os.path.exists(TOKEN_PROXY_FILE):
        TOKEN_PROXY_MAP = load_token_proxy_map()
        TOKENS = list(TOKEN_PROXY_MAP.keys())
        PROXIES = list(TOKEN_PROXY_MAP.values())
        
        if not TOKEN_PROXY_MAP:
            print(Fore.RED + f"❌ Nenhum token/proxy encontrado no arquivo {TOKEN_PROXY_FILE}")
            print(Fore.YELLOW + f"⚠️ Por favor, edite o arquivo e adicione seus tokens e proxies antes de continuar.")
            print(Fore.YELLOW + f"⚠️ Formato: token|proxy (um por linha)")
            sys.exit(1)
    
    # Se houver argumento "auto", iniciar automaticamente o fluxo contínuo
    if len(sys.argv) > 1 and sys.argv[1] == "auto":
        print(Fore.GREEN + "💻 Iniciando modo automático de criação e gerenciamento de VMs")
        asyncio.run(loop_opcoes_1_e_2())
    else:
        # Menu interativo
        print(Fore.CYAN + "💻 Sistema de Gerenciamento de VMs Doprax")
        print(Fore.YELLOW + "Dica: Execute com 'python dopraxCreate.py auto' para iniciar o modo automático diretamente")
        asyncio.run(main_menu())