#!/usr/bin/env python3
import asyncio
import asyncssh
import sys
from colorama import Fore, init

# Inicializar colorama
init(autoreset=True)

# Configurações
SSH_PORT = 22
SSH_USERNAME = "root"
SSH_PASSWORD = "eE5522892004@@e"
IP_TO_CHECK = "*************"  # IP da máquina a ser verificada

async def corrigir_servidor():
    try:
        print(Fore.YELLOW + f"🔄 Conectando via SSH a {IP_TO_CHECK}...")
        
        # Tentar conectar via SSH
        conn = await asyncssh.connect(
            IP_TO_CHECK, 
            port=SSH_PORT, 
            username=SSH_USERNAME, 
            password=SSH_PASSWORD, 
            known_hosts=None  # Ignora verificação de known_hosts
        )
        
        print(Fore.GREEN + f"✅ Conexão SSH estabelecida com {IP_TO_CHECK}")
        
        # Verificar a linha com erro
        print(Fore.YELLOW + "🔄 Verificando a linha com erro (linha 1159)...")
        result = await conn.run("docker exec api-enviador bash -c 'sed -n \"1159p\" /app/server.js'")
        print(Fore.CYAN + "=== Linha com erro no arquivo server.js ===")
        print(result.stdout)
        
        # Fazer backup do arquivo server.js
        print(Fore.YELLOW + "🔄 Fazendo backup do arquivo server.js...")
        await conn.run("docker exec api-enviador bash -c 'cp /app/server.js /app/server.js.bak.line1159'")
        print(Fore.GREEN + "✅ Backup criado em /app/server.js.bak.line1159")
        
        # Corrigir a linha com erro
        print(Fore.YELLOW + "🔄 Corrigindo a linha com erro...")
        await conn.run("docker exec api-enviador bash -c \"sed -i '1159s/logger.error(\"Erro na campanha \\${req.params.id}: \\${error.message}\\`);/logger.error(\"Erro na campanha \" + req.params.id + \": \" + error.message);/g' /app/server.js\"")
        
        # Verificar se a correção foi aplicada
        print(Fore.YELLOW + "🔄 Verificando se a correção foi aplicada...")
        result = await conn.run("docker exec api-enviador bash -c 'sed -n \"1159p\" /app/server.js'")
        print(Fore.CYAN + "=== Linha corrigida no arquivo server.js ===")
        print(result.stdout)
        
        # Tentar iniciar o servidor manualmente para verificar se os erros foram corrigidos
        print(Fore.YELLOW + "🔄 Tentando iniciar o servidor manualmente para verificar se os erros foram corrigidos...")
        result = await conn.run("docker exec api-enviador bash -c 'cd /app && node server.js' 2>&1 || echo 'Ainda há erros no servidor'")
        print(Fore.CYAN + "=== Resultado da Tentativa de Iniciar o Servidor ===")
        print(result.stdout)
        
        # Se ainda houver erros, editar o arquivo diretamente
        if "SyntaxError" in result.stdout:
            print(Fore.YELLOW + "🔄 Ainda há erros. Editando o arquivo diretamente...")
            await conn.run("docker exec api-enviador bash -c 'echo \"        logger.error(\\\"Erro na campanha \\\" + req.params.id + \\\": \\\" + error.message);\" > /tmp/line1159.txt'")
            await conn.run("docker exec api-enviador bash -c 'sed -i \"1159s/.*$/$(cat /tmp/line1159.txt)/\" /app/server.js'")
            
            # Verificar novamente
            print(Fore.YELLOW + "🔄 Verificando novamente se a correção foi aplicada...")
            result = await conn.run("docker exec api-enviador bash -c 'sed -n \"1159p\" /app/server.js'")
            print(Fore.CYAN + "=== Linha corrigida no arquivo server.js ===")
            print(result.stdout)
            
            # Tentar iniciar o servidor novamente
            print(Fore.YELLOW + "🔄 Tentando iniciar o servidor novamente...")
            result = await conn.run("docker exec api-enviador bash -c 'cd /app && node server.js' 2>&1 || echo 'Ainda há erros no servidor'")
            print(Fore.CYAN + "=== Resultado da Tentativa de Iniciar o Servidor ===")
            print(result.stdout)
        
        # Reiniciar o contêiner
        print(Fore.YELLOW + "🔄 Reiniciando o contêiner api-enviador...")
        await conn.run("docker restart api-enviador")
        print(Fore.GREEN + "✅ Contêiner reiniciado")
        
        # Aguardar um pouco para o serviço iniciar
        print(Fore.YELLOW + "🔄 Aguardando o serviço iniciar...")
        await asyncio.sleep(10)
        
        # Verificar status do contêiner
        print(Fore.YELLOW + "🔄 Verificando status do contêiner...")
        result = await conn.run("docker ps -a --filter name=api-enviador --format '{{.Names}} {{.Status}}'")
        print(Fore.CYAN + "=== Status do Contêiner api-enviador ===")
        print(result.stdout)
        
        # Verificar logs do contêiner
        print(Fore.YELLOW + "🔄 Verificando logs recentes do contêiner...")
        result = await conn.run("docker logs --tail 20 api-enviador")
        print(Fore.CYAN + "=== Logs Recentes do Contêiner api-enviador ===")
        print(result.stdout)
        
        # Fechar conexão SSH
        conn.close()
        print(Fore.GREEN + "✅ Correção concluída")
        
    except Exception as e:
        print(Fore.RED + f"❌ Erro: {e}")
        return False

async def main():
    await corrigir_servidor()

if __name__ == "__main__":
    asyncio.run(main())
