import dearpygui.dearpygui as dpg
import asyncio
import json
import os
import logging
import mimetypes
import time
from threading import Lock
from collections import deque
from datetime import datetime, timedelta
from tempfile import NamedTemporaryFile
import uuid, traceback
from concurrent.futures import ThreadPoolExecutor
import requests
import database_managerEnvio

# Configuração de logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('campaign_manager.log'),
        logging.StreamHandler()
    ]
)

class EmailServer:
    def __init__(self, config):
        self.nome = config['nome']
        self.url = config['url'].rstrip('/')
        self.ativo = config.get('ativo', False)
        self.campaign_id = None
        self.status = "🟡 Inicializando"
        self.estatisticas = {}
        self.inicio_campanha = None
        self.session = requests.Session()
        self.session.headers.update({'User-Agent': 'PythonCampaignManager/2.1'})

    async def verificar_status_postfix_async(self):
        try:
            response = await asyncio.to_thread(
                self.session.get,
                f"{self.url}/postfix-status",
                timeout=60
            )
            return response.status_code == 200 and response.json().get('emailStatus') == 'Entregue'
        except Exception as e:
            return False

    async def monitorar_campanha_async(self):
        try:
            if not self.campaign_id:
                return None

            response = await asyncio.to_thread(
                self.session.get,
                f"{self.url}/campanha/{self.campaign_id}",
                timeout=60
            )

            if response.status_code == 200:
                data = response.json()
                self.estatisticas = data.get('estatisticas', {})
                self.status = data.get('status', '🔴 Status desconhecido')

                # Calcular taxa de envio
                if self.inicio_campanha:
                    tempo_decorrido = (datetime.now() - self.inicio_campanha).total_seconds()
                    self.estatisticas['taxa_envio'] = self.estatisticas.get('enviados', 0) / tempo_decorrido if tempo_decorrido > 0 else 0
                return data
            return None
        except Exception as e:
            self.status = "🔴 Erro de monitoramento"
            return None

class GerenciadorCampanhas:
    def __init__(self, config_path='servers.json'):
        try:
            # Carregar apenas as configurações de email do JSON
            with open(config_path) as f:
                self.config = json.load(f)

            # Garantir que não há dependência de servidores no JSON
            if 'servidores' in self.config:
                del self.config['servidores']

            self.servidores = []
            self.total_emails = 0
            self.emails_por_servidor = {}

            self._validar_configuracao()

        except Exception as e:
            logging.critical(f"Erro ao carregar configuração: {str(e)}")
            raise

    def _validar_configuracao(self):
        required_keys = ['senders', 'subjects']
        for key in required_keys:
            if key not in self.config:
                raise ValueError(f"Chave obrigatória ausente: {key}")

        if not isinstance(self.config['senders'], list) or len(self.config['senders']) == 0:
            raise ValueError("Lista de remetentes inválida ou vazia")

        if not isinstance(self.config['subjects'], list) or len(self.config['subjects']) == 0:
            raise ValueError("Lista de assuntos inválida ou vazia")

    async def filtrar_servidores_ativos_async(self):
        self.servidores = []
        tasks = []

        # Obter servidores do banco de dados em vez do JSON
        db_servers = database_managerEnvio.get_available_servers()

        for server_data in db_servers:
            # Desempacotar os valores do servidor
            # id, linode_id, label, ip, url, status, email_status, token, created_at
            server_id = server_data[0]
            server_nome = server_data[2]  # label
            server_url = server_data[4]   # url

            server_config = {
                'nome': server_nome,
                'url': server_url,
                'ativo': True
            }

            server = EmailServer(server_config)
            tasks.append(server.verificar_status_postfix_async())
            self.servidores.append(server)

        resultados = await asyncio.gather(*tasks)

        valid_servers = []
        for server, valido in zip(self.servidores, resultados):
            if valido:
                valid_servers.append(server)
            else:
                logging.warning(f"Servidor {server.nome} inativo ou offline")
                # Atualizar status no banco de dados
                # Encontrar o ID do servidor para atualizar
                for server_data in db_servers:
                    if server_data[2] == server.nome:  # Comparar pelo label
                        database_managerEnvio.update_server_status(server_data[0], email_status="Offline")
                        break

        self.servidores = valid_servers

    def _dividir_emails_round_robin(self, emails, num_servers):
        chunks = [[] for _ in range(num_servers)]
        for index, email in enumerate(emails):
            chunks[index % num_servers].append(email)
        return chunks

    def carregar_emails(self, arquivo_emails):
        try:
            with open(arquivo_emails, 'r', encoding='utf-8') as f:
                lines = [line.strip() for line in f if line.strip()]

            # Verifica se a primeira linha contém headers
            has_headers = any(col in lines[0].lower() for col in ['nome', 'email', 'cpf'])

            if has_headers:
                headers = lines[0]  # Salva a linha de headers
                emails = lines[1:]  # Remove a linha de headers dos dados
            else:
                headers = None
                emails = lines

            self.total_emails = len(emails)
            num_servers = len(self.servidores)

            if num_servers == 0:
                raise ValueError("Nenhum servidor disponível para distribuição")

            chunks = self._dividir_emails_round_robin(emails, num_servers)

            for server, chunk in zip(self.servidores, chunks):
                # Se houver headers, adiciona ao chunk
                if has_headers:
                    chunk_com_headers = [headers] + chunk
                else:
                    chunk_com_headers = chunk

                self.emails_por_servidor[server.nome] = chunk_com_headers

            logging.info(f"Emails carregados e distribuídos em {num_servers} servidores")

        except Exception as e:
            logging.error(f"Erro ao carregar emails: {str(e)}")
            raise

    async def _upload_arquivo_async(self, session, url, campo, caminho_arquivo, nome_arquivo, mime_type):
        try:
            if not os.path.exists(caminho_arquivo):
                raise FileNotFoundError(f"Arquivo não encontrado: {caminho_arquivo}")

            headers = {
                'X-Request-ID': str(uuid.uuid4()),
                'X-File-Type': mime_type
            }

            # Adicionando lógica de retry
            max_retries = 3
            retry_delay = 5  # segundos

            for attempt in range(1, max_retries + 1):
                try:
                    def _upload_sincrono():
                        with open(caminho_arquivo, 'rb') as f:
                            files = {campo: (nome_arquivo, f, mime_type)}
                            return session.post(url, files=files, headers=headers, timeout=60)

                    response = await asyncio.to_thread(_upload_sincrono)

                    if response.status_code != 200:
                        error_data = response.json() if response.content else {"error": "Sem resposta"}
                        error_msg = error_data.get('error', 'Erro desconhecido')
                        logging.error(f"ERRO {response.status_code} - {error_msg}")

                        if attempt < max_retries:
                            logging.info(f"Tentativa {attempt} falhou. Tentando novamente em {retry_delay} segundos...")
                            await asyncio.sleep(retry_delay)
                            continue
                        return False

                    return response.json().get('success', False)
                except (requests.RequestException, ConnectionError, TimeoutError) as e:
                    if attempt < max_retries:
                        logging.warning(f"Falha na conexão na tentativa {attempt}: {str(e)}. Tentando novamente em {retry_delay} segundos...")
                        await asyncio.sleep(retry_delay)
                    else:
                        logging.error(f"Falha na conexão após {max_retries} tentativas: {str(e)}")
                        return False
                except Exception as e:
                    logging.error(f"Erro não relacionado à conexão: {str(e)}")
                    return False

            return False  # Alcançou número máximo de tentativas sem sucesso

        except Exception as e:
            logging.error(f"Falha crítica no upload: {str(e)}")
            return False

    async def _processar_servidor_async(self, server, html_path):
        temp_path = None
        try:
            server.inicio_campanha = datetime.now()

            if not html_path.lower().endswith('.html'):
                raise ValueError("Arquivo HTML deve ter extensão .html")

            def _validar_html():
                with open(html_path, 'rb') as f:
                    if b'<!DOCTYPE html>' not in f.read(1024):
                        raise ValueError("Arquivo HTML inválido - falta declaração DOCTYPE")

            await asyncio.to_thread(_validar_html)

            if not await self._upload_arquivo_async(
                server.session,
                f"{server.url}/upload-html",
                'html',
                html_path,
                os.path.basename(html_path),
                'text/html'
            ):
                raise ValueError("Falha no upload do HTML")

            emails = self.emails_por_servidor[server.nome]
            if not emails:
                raise ValueError("Nenhum email atribuído ao servidor")

            def _criar_arquivo_temporario():
                with NamedTemporaryFile(mode='w+', encoding='utf-8', suffix='.txt', delete=False) as tmp_file:
                    tmp_file.write('\n'.join(emails))
                    return tmp_file.name

            temp_path = await asyncio.to_thread(_criar_arquivo_temporario)

            if not await self._upload_arquivo_async(
                server.session,
                f"{server.url}/upload-list",
                'emails',
                temp_path,
                f'lista_{server.nome}.txt',
                'text/plain'
            ):
                raise ValueError("Falha no upload da lista de emails")

            payload = {
                "senders": self.config['senders'],
                "subjects": self.config['subjects']
            }

            def _iniciar_campanha():
                return server.session.post(
                    f"{server.url}/start-campaign",
                    json=payload,
                    timeout=60
                )

            response = await asyncio.to_thread(_iniciar_campanha)

            if response.status_code != 200:
                error_data = response.json()
                raise ValueError(f"{error_data.get('error')} | Código: {response.status_code}")

            response_data = response.json()
            if not response_data.get('success'):
                raise ValueError(response_data.get('error', 'Erro desconhecido no servidor'))

            server.campaign_id = response_data['campaignId']
            server.status = "🟢 Campanha em andamento"

        except Exception as e:
            server.status = f"🔴 {str(e)[:100]}"
            logging.error(f"Erro no processamento do servidor {server.nome}: {str(e)}")
            raise
        finally:
            if temp_path and os.path.exists(temp_path):
                try:
                    await asyncio.to_thread(os.remove, temp_path)
                except Exception as e:
                    logging.error(f"Erro ao limpar arquivo temporário: {str(e)}")

    async def distribuir_campanhas_async(self, html_path):
        tasks = []
        for server in self.servidores:
            task = self._processar_servidor_async(server, html_path)
            tasks.append(task)
        await asyncio.gather(*tasks)

    async def monitorar_async(self):
        try:
            while True:
                tasks = [server.monitorar_campanha_async() for server in self.servidores]
                resultados = await asyncio.gather(*tasks)

                if all(server.status == "concluída" for server in self.servidores):
                    logging.info("\n✅ Todas as campanhas concluídas com sucesso!")
                    break

                await asyncio.sleep(30)  # Intervalo aumentado para 10 segundos

        except KeyboardInterrupt:
            logging.info("\n⚠️ Monitoramento interrompido")

    def _encerrar_campanha_por_inatividade(self, total_enviados):
        self.gerar_relatorio()
        pendentes = self._coletar_pendentes(total_enviados)
        self.salvar_pendentes(pendentes)

        for server in self.servidores:
            server.status = "🟡 Encerrado por inatividade"

        logging.info(f"📁 Pendentes salvos em: {os.path.abspath('pendentes/pendentesReenvio.txt')}")

    def _coletar_pendentes(self, total_enviados):
        pendentes = []

        for server in self.servidores:
            try:
                response = server.session.get(
                    f"{self.url}/campanha/{server.campaign_id}/detalhes",
                    timeout=30
                )

                if response.status_code == 200:
                    detalhes = response.json().get('detalhes', [])
                    pendentes += [
                        entry['email'] for entry in detalhes
                        if entry.get('status') == 'pendente'
                    ]
            except Exception as e:
                logging.error(f"Erro ao coletar pendentes de {server.nome}: {str(e)}")

        if not pendentes:
            total_pendentes = self.total_emails - total_enviados
            pendentes = [f"email_pendente_{i}@dominio.com" for i in range(total_pendentes)]

        return list(set(pendentes))

    def salvar_pendentes(self, emails):
        os.makedirs("pendentes", exist_ok=True)
        caminho = os.path.join("pendentes", "pendentesReenvio.txt")

        try:
            with open(caminho, 'w', encoding='utf-8') as f:
                f.write('\n'.join(emails))
        except Exception as e:
            logging.error(f"Erro ao salvar pendentes: {str(e)}")

    def gerar_relatorio(self):
        print("\n" + "="*80)
        print("📊 RELATÓRIO FINAL DAS CAMPANHAS")
        print("="*80)

        total_entregues = 0
        total_pendentes = 0

        print(f"\n{'Servidor':<20} | {'ID Campanha':<36} | {'Status':<20} | {'Enviados':<10} | {'Pendentes':<10}")
        print("-"*95)

        for server in self.servidores:
            estatisticas = server.estatisticas or {}
            enviados = estatisticas.get('enviados', 0)
            pendentes = estatisticas.get('pendentes', 0)

            total_entregues += enviados
            total_pendentes += pendentes

            print(f"{server.nome:<20} | {server.campaign_id or 'N/A':<36} | "
                f"{server.status:<20} | {enviados:<10} | {pendentes:<10}")

        print("\n" + "="*80)
        print("📈 ESTATÍSTICAS CONSOLIDADAS")
        print(f"Total de Emails Processados: {self.total_emails}")
        print(f"Taxa de Entrega: {(total_entregues/self.total_emails*100 if self.total_emails > 0 else 0):.2f}%")
        print(f"Emails Pendentes: {total_pendentes}")
        print(f"Servidores Utilizados: {len(self.servidores)}")
        print("="*80 + "\n")

    async def iniciar_envios_async(self, html_path, lista_emails):
        try:
            logging.info("🚀 Iniciando processo de envio de campanhas")

            await self.filtrar_servidores_ativos_async()

            if not self.servidores:
                logging.error("❌ Nenhum servidor operacional disponível")
                return

            self.carregar_emails(lista_emails)
            await self.distribuir_campanhas_async(html_path)
            await self.monitorar_async()
            self.gerar_relatorio()

        except KeyboardInterrupt:
            logging.info("\n🚫 Operação interrompida pelo usuário")
        except Exception as e:
            logging.error(f"💣 Erro fatal no processo de envio: {str(e)}")
            raise

class GUIHandler(logging.Handler):
    def __init__(self, log_window):
        super().__init__()
        self.log_window = log_window
        self.max_lines = 100

    def emit(self, record):
        try:
            msg = self.format(record)
            dpg.add_text(msg, parent=self.log_window)

            # Limitar número de linhas
            children = dpg.get_item_children(self.log_window, 1)
            if len(children) > self.max_lines:
                for _ in range(len(children) - self.max_lines):
                    dpg.delete_item(children[0])
        except Exception:
            self.handleError(record)

class CampaignMonitorGUI:
    def __init__(self, gerenciador):
        self.gerenciador = gerenciador
        self.lista_emails = None
        self.html_path = None
        self.table_pool = []
        self.gui_queue = []
        self.gui_lock = Lock()
        self.update_interval = 5  # Intervalo padrão de atualização
        self.last_update = time.time()

        # Configuração do loop de eventos assíncrono
        self.loop = asyncio.get_event_loop()
        self.setup_gui()
        self.create_themes()
        self.setup_logging()
        self.loop.create_task(self.process_gui_updates())
        self.atualizar_contagem_inicial()

    def atualizar_contagem_inicial(self):
        # Obter contagem de servidores do banco de dados em vez do JSON
        db_servers = database_managerEnvio.get_available_servers()
        total = len(db_servers)
        dpg.set_value(self.total_servidores, f"Servidores Totais: {total}")
        dpg.set_value(self.servidores_ativos, f" | Ativos: 0")

    def setup_logging(self):
        logger = logging.getLogger()
        logger.setLevel(logging.DEBUG)
        gui_handler = GUIHandler(log_window=self.log_window)
        gui_handler.setLevel(logging.DEBUG)
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        gui_handler.setFormatter(formatter)
        logger.addHandler(gui_handler)

    def create_themes(self):
        with dpg.theme() as green_theme:
            with dpg.theme_component(dpg.mvText):
                dpg.add_theme_color(dpg.mvThemeCol_Text, (0, 255, 0, 255))

        with dpg.theme() as yellow_theme:
            with dpg.theme_component(dpg.mvText):
                dpg.add_theme_color(dpg.mvThemeCol_Text, (255, 255, 0, 255))

        with dpg.theme() as red_theme:
            with dpg.theme_component(dpg.mvText):
                dpg.add_theme_color(dpg.mvThemeCol_Text, (255, 0, 0, 255))

        self.themes = {
            "green": green_theme,
            "yellow": yellow_theme,
            "red": red_theme
        }

    def setup_gui(self):
        dpg.create_context()
        dpg.create_viewport(title='Monitor de Campanhas', width=1280, height=720)
        dpg.setup_dearpygui()

        with dpg.window(label="Dashboard Principal", tag="main_window", width=1260, height=700):
            # Cabeçalho de status
            with dpg.group(horizontal=True):
                self.total_servidores = dpg.add_text("Servidores Totais: 0")
                self.servidores_ativos = dpg.add_text(" | Ativos: 0")
                self.emails_enviados = dpg.add_text(" | Enviados: 0")
                self.emails_pendentes = dpg.add_text(" | Pendentes: 0")

            # Barra de ferramentas
            with dpg.group(horizontal=True):
                dpg.add_button(
                    label="Verificar Status Servidores",
                    width=200,
                    callback=self.verificar_status_servidores
                )
                dpg.add_button(
                    label="Selecionar Lista de Emails",
                    width=200,
                    callback=self.selecionar_lista_emails
                )
                dpg.add_button(
                    label="Selecionar Template HTML",
                    width=200,
                    callback=self.selecionar_arquivo_html
                )
                dpg.add_button(
                    label="Iniciar Campanha",
                    width=200,
                    callback=self.iniciar_campanha
                )

            # Tabela de servidores com pool de linhas
            with dpg.table(
                tag="tabela_principal",
                header_row=True,
                policy=dpg.mvTable_SizingStretchProp,
                borders_innerH=True,
                borders_outerH=True,
                borders_innerV=True,
                borders_outerV=True,
                row_background=True,
                scrollY=True,
                height=450,
                width=1240
            ):
                for _ in range(5):  # Colunas fixas
                    dpg.add_table_column()

            # Área de logs
            with dpg.child_window(
                label="Logs do Sistema",
                height=180,
                width=1240,
                border=True
            ) as log_window:
                self.log_window = log_window

        dpg.set_primary_window("main_window", True)
        dpg.show_viewport()

    async def safe_gui_update(self, callback, *args):
        with self.gui_lock:
            self.gui_queue.append((callback, args))

    async def process_gui_updates(self):
        while dpg.is_dearpygui_running():
            try:
                updates = []
                with self.gui_lock:
                    updates = self.gui_queue.copy()
                    self.gui_queue.clear()

                for callback, args in updates:
                    try:
                        callback(*args)
                    except Exception as e:
                        logging.error(f"Erro na atualização GUI: {str(e)}")

                await asyncio.sleep(0.01)
            except Exception as e:
                logging.error(f"Erro no processamento de updates: {str(e)}")
                await asyncio.sleep(1)

    def verificar_status_servidores(self):
        async def task_verificacao():
            await self.gerenciador.filtrar_servidores_ativos_async()

            # Atualizar contagens
            # Obter contagem de servidores disponíveis do banco de dados
            db_servers = database_managerEnvio.get_available_servers()
            total = len(db_servers)
            ativos = len(self.gerenciador.servidores)

            await self.safe_gui_update(
                dpg.set_value,
                self.total_servidores,
                f"Servidores Totais: {total}"
            )
            await self.safe_gui_update(
                dpg.set_value,
                self.servidores_ativos,
                f" | Ativos: {ativos}"
            )
            await self.safe_gui_update(self.inicializar_tabela)
            logging.info("Status dos servidores verificado")

        self.loop.call_soon_threadsafe(
            lambda: self.loop.create_task(task_verificacao())
        )

    def inicializar_tabela(self):
        try:
            # Limpar e reutilizar linhas existentes
            existing_rows = dpg.get_item_children("tabela_principal", 1)
            for row in existing_rows:
                dpg.delete_item(row)

            # Adicionar cabeçalhos
            with dpg.table_row(parent="tabela_principal"):
                for header in ["Servidor", "Status", "Enviados", "Pendentes", "Taxa/s"]:
                    dpg.add_text(header)

            # Pré-alocar linhas baseado nos servidores
            for server in self.gerenciador.servidores:
                with dpg.table_row(parent="tabela_principal") as row_id:
                    cells = [
                        dpg.add_text(server.nome),
                        dpg.add_text(server.status),
                        dpg.add_text("0"),
                        dpg.add_text("0"),
                        dpg.add_text("0.0")
                    ]
                    # Aplicar tema inicial
                    theme = self.themes["red"]
                    if "🟢" in server.status:
                        theme = self.themes["green"]
                    elif "🟡" in server.status:
                        theme = self.themes["yellow"]
                    dpg.bind_item_theme(cells[1], theme)
        except Exception as e:
            logging.error(f"Erro ao inicializar tabela: {str(e)}")

    def selecionar_lista_emails(self):
        with dpg.file_dialog(
            directory_selector=False,
            show=True,
            callback=lambda s, a: self._carregar_lista(a['file_path_name']),
            width=800,
            height=500
        ):
            dpg.add_file_extension(".txt")

    def _carregar_lista(self, caminho):
        self.lista_emails = caminho
        logging.info(f"Lista de emails carregada: {caminho}")

    def selecionar_arquivo_html(self):
        with dpg.file_dialog(
            directory_selector=False,
            show=True,
            callback=lambda s, a: self._carregar_html(a['file_path_name']),
            width=800,
            height=500
        ):
            dpg.add_file_extension(".html")

    def _carregar_html(self, caminho):
        self.html_path = caminho
        logging.info(f"Template HTML carregado: {caminho}")

    def iniciar_campanha(self):
        if not all([self.lista_emails, self.html_path]):
            logging.error("Selecione ambos os arquivos antes de iniciar!")
            return

        async def task_envio():
            await self.safe_gui_update(self.inicializar_tabela)
            try:
                await self.gerenciador.iniciar_envios_async(
                    self.html_path,
                    self.lista_emails
                )
            except Exception as e:
                logging.error(f"Falha na campanha: {str(e)}")

        self.loop.call_soon_threadsafe(
            lambda: self.loop.create_task(task_envio())
        )

    async def atualizar_interface(self):
        while dpg.is_dearpygui_running():
            try:
                current_time = time.time()
                if (current_time - self.last_update) > self.update_interval:
                    if self.gerenciador.servidores:
                        # Coletar dados assincronamente
                        tasks = [s.monitorar_campanha_async() for s in self.gerenciador.servidores]
                        resultados = await asyncio.gather(*tasks)

                        # Processar estatísticas - protegendo contra NoneType
                        total_envios = sum(r.get('estatisticas', {}).get('enviados', 0)
                                         for r in resultados if r is not None)
                        total_pendentes = sum(r.get('estatisticas', {}).get('pendentes', 0)
                                            for r in resultados if r is not None)

                        # Obter contagem total de servidores do banco de dados
                        db_servers = database_managerEnvio.get_available_servers()
                        total = len(db_servers)
                        ativos = len(self.gerenciador.servidores)

                        await self.safe_gui_update(
                            dpg.set_value,
                            self.total_servidores,
                            f"Servidores Totais: {total}"
                        )
                        await self.safe_gui_update(
                            dpg.set_value,
                            self.servidores_ativos,
                            f" | Ativos: {ativos}"
                        )

                        await self.safe_gui_update(
                            dpg.set_value,
                            self.emails_enviados,
                            f" | Enviados: {total_envios}"
                        )
                        await self.safe_gui_update(
                            dpg.set_value,
                            self.emails_pendentes,
                            f" | Pendentes: {total_pendentes}"
                        )

                        # Atualizar linhas da tabela
                        rows = dpg.get_item_children("tabela_principal", 1)[1:]  # Pular cabeçalho
                        for idx, (server, dados) in enumerate(zip(self.gerenciador.servidores, resultados)):
                            if idx >= len(rows) or dados is None:
                                continue

                            cells = dpg.get_item_children(rows[idx], 1)
                            if len(cells) >= 5:
                                # Atualizar valores via fila segura
                                await self.safe_gui_update(dpg.set_value, cells[1], server.status)

                                estatisticas = dados.get('estatisticas', {})
                                await self.safe_gui_update(dpg.set_value, cells[2], str(estatisticas.get('enviados', 0)))
                                await self.safe_gui_update(dpg.set_value, cells[3], str(estatisticas.get('pendentes', 0)))
                                await self.safe_gui_update(dpg.set_value, cells[4], f"{estatisticas.get('taxa_envio', 0):.1f}")

                                # Determinar tema
                                theme = self.themes["red"]
                                if "🟢" in server.status:
                                    theme = self.themes["green"]
                                elif "🟡" in server.status:
                                    theme = self.themes["yellow"]

                                await self.safe_gui_update(dpg.bind_item_theme, cells[1], theme)

                    self.last_update = current_time

                await asyncio.sleep(0.1)  # Mantém responsividade

            except Exception as e:
                logging.error(f"Erro na atualização: {str(e)}")
                await asyncio.sleep(5)

    async def executar(self):
        try:
            self.loop.create_task(self.atualizar_interface())
            while dpg.is_dearpygui_running():
                await asyncio.sleep(0.01)
                dpg.render_dearpygui_frame()
        finally:
            dpg.destroy_context()

if __name__ == "__main__":
    try:
        manager = GerenciadorCampanhas()
        gui = CampaignMonitorGUI(manager)
        gui.loop.run_until_complete(gui.executar())
    except Exception as e:
        logging.critical(f"Falha crítica: {str(e)}")
        print(traceback.format_exc())  # Exibe o traceback completoDA