import requests
import os
import time
import json
import math
import sqlite3
import asyncio
import aiohttp
import random
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor
import traceback

# Configurações do banco de dados
DB_PATH = "linodes.db"
SENT_EMAILS_DB = "sent_emails.db"

class PostfixCampaignSender:
    def __init__(self, servers=None):
        """
        Inicializa o sender com servidores ativos do banco de dados ou lista fornecida

        Args:
            servers: Lista opcional de servidores no formato [{'ip': ip, 'token': token}]
                    Se None, carrega do banco de dados
        """
        self.servers = servers or self._load_active_servers()
        self.sessions = [requests.Session() for _ in self.servers]

        for session in self.sessions:
            session.headers.update({
                'User-Agent': 'PostfixCampaignSender/1.0',
                'Accept': 'application/json'
            })

        # Inicializar banco de dados de emails enviados
        self._init_sent_emails_db()

    def _init_sent_emails_db(self):
        """Inicializa o banco de dados de emails enviados"""
        try:
            conn = sqlite3.connect(SENT_EMAILS_DB)
            c = conn.cursor()
            c.execute('''
                CREATE TABLE IF NOT EXISTS sent_emails (
                    email TEXT PRIMARY KEY,
                    cpf TEXT,
                    sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    campaign_id TEXT,
                    server_ip TEXT,
                    reserved INTEGER DEFAULT 0,
                    reserved_for TEXT
                )
            ''')
            conn.commit()
            conn.close()
            print("Campaign tracking database initialized successfully")
        except Exception as e:
            print(f"Erro ao inicializar banco de dados de emails enviados: {e}")

    def _mark_emails_as_sent(self, emails_data, campaign_id, server_ip):
        """
        Marca emails como enviados no banco de dados

        Args:
            emails_data: Lista de dicionários com 'email' e 'cpf'
            campaign_id: ID da campanha
            server_ip: IP do servidor que enviou
        """
        try:
            conn = sqlite3.connect(SENT_EMAILS_DB)
            c = conn.cursor()

            for data in emails_data:
                c.execute('''
                    INSERT OR REPLACE INTO sent_emails (email, cpf, campaign_id, server_ip, reserved, reserved_for)
                    VALUES (?, ?, ?, ?, 0, NULL)
                ''', (data['email'], data.get('cpf', ''), campaign_id, server_ip))

            conn.commit()
            conn.close()
            print(f"Marcados {len(emails_data)} emails como enviados")
        except Exception as e:
            print(f"Erro ao marcar emails como enviados: {e}")

    def _load_active_servers(self):
        """Carrega servidores ativos com status 'Entregue' do banco de dados"""
        try:
            conn = sqlite3.connect(DB_PATH)
            c = conn.cursor()
            c.execute('''
                SELECT ip, token FROM serversAtivos
                WHERE email_status = 'Entregue' AND status = 1
            ''')
            servers = [{'ip': row[0], 'token': row[1]} for row in c.fetchall()]
            conn.close()

            print(f"Carregados {len(servers)} servidores ativos do banco de dados")
            return servers
        except Exception as e:
            print(f"Erro ao carregar servidores ativos: {e}")
            return []



    def reserve_emails_for_server(self, email_list_path, server_ip, limit=None, server_index=None, total_servers=None):
        """
        Reserva emails para um servidor específico usando o banco de dados e persistência de índice

        Args:
            email_list_path: Caminho para o arquivo de emails
            server_ip: IP do servidor
            limit: Número máximo de emails a reservar
            server_index: Índice do servidor (não usado mais, mantido para compatibilidade)
            total_servers: Número total de servidores (não usado mais, mantido para compatibilidade)
        """
        try:
            # Ler o arquivo de emails
            with open(email_list_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            # Remove linhas vazias
            lines = [line.strip() for line in lines if line.strip()]

            # Verificar o formato do arquivo
            # Se o arquivo tiver apenas emails, um por linha, sem vírgulas
            if len(lines) > 0 and ',' not in lines[0]:
                print("Arquivo no formato simples: um email por linha")
                header = "email"
                has_header = False
                email_idx = 0
                cpf_idx = None
            else:
                # Verificar se tem cabeçalho
                has_header = False
                if len(lines) > 0 and ',' in lines[0]:
                    first_line = lines[0].lower()
                    if 'email' in first_line:
                        has_header = True
                        header = lines[0]
                else:
                    header = "nome,email,cpf"

                # Determinar índices dos campos
                header_fields = header.lower().split(',')
                email_idx = None
                cpf_idx = None

                for i, field in enumerate(header_fields):
                    if 'email' in field:
                        email_idx = i
                    elif 'cpf' in field or 'documento' in field:
                        cpf_idx = i

                # Se não encontrou campo de email, tenta assumir posição padrão
                if email_idx is None:
                    if len(header_fields) >= 2:
                        email_idx = 1  # Assume que email é o segundo campo
                    else:
                        print("Não foi possível identificar o campo de email no arquivo")
                        return 0

            # Separar o cabeçalho se existir
            if has_header:
                data_lines = lines[1:]
            else:
                data_lines = lines

            # Arquivo de índice para persistência
            index_file = f"{email_list_path}.index"

            # Verificar se existe arquivo de índice
            start_idx = 0
            if os.path.exists(index_file):
                try:
                    with open(index_file, 'r') as f:
                        start_idx = int(f.read().strip())
                    print(f"Índice persistente carregado: {start_idx}")
                except Exception as e:
                    print(f"Erro ao ler arquivo de índice: {e}")
                    start_idx = 0

            # Calcular o tamanho do lote para este servidor
            total_emails = len(data_lines)
            batch_size = limit if limit else 5000  # Padrão de 5000 emails por servidor

            # Calcular o fim do lote para este servidor
            end_idx = min(start_idx + batch_size, total_emails)

            # Verificar se há emails suficientes
            if start_idx >= total_emails:
                print(f"Não há mais emails disponíveis no arquivo (índice atual: {start_idx}, total: {total_emails})")
                return 0

            # Obter o lote de emails para este servidor
            server_emails = data_lines[start_idx:end_idx]
            print(f"Servidor {server_ip}: processando emails {start_idx+1} a {end_idx} de {total_emails}")

            # Atualizar o arquivo de índice para o próximo servidor
            try:
                with open(index_file, 'w') as f:
                    f.write(str(end_idx))
                print(f"Índice atualizado para: {end_idx}")
            except Exception as e:
                print(f"Erro ao atualizar arquivo de índice: {e}")

            # Conectar ao banco de dados
            conn = sqlite3.connect(SENT_EMAILS_DB)
            c = conn.cursor()

            # Limpar reservas anteriores para este servidor
            c.execute('DELETE FROM sent_emails WHERE reserved = 1 AND reserved_for = ?', (server_ip,))

            # Processar as linhas e inserir no banco
            reserved_count = 0

            # Criar arquivo temporário para este servidor
            temp_path = f"temp_emails_server_{server_ip.replace('.', '_')}.txt"
            with open(temp_path, 'w', encoding='utf-8') as f:
                f.write(f"{header}\n")  # Escreve o cabeçalho

                for line in server_emails:
                    if not line.strip():
                        continue

                    # Se o arquivo for no formato simples (sem vírgulas)
                    if ',' not in line and email_idx == 0:
                        email = line.strip().lower()
                        cpf = ''
                    else:
                        fields = line.split(',')
                        if len(fields) <= email_idx:
                            continue  # Linha inválida

                        email = fields[email_idx].strip().lower()
                        cpf = fields[cpf_idx].strip() if cpf_idx is not None and len(fields) > cpf_idx else ''

                    # Verificar se o email já foi enviado ou reservado para outro servidor
                    c.execute('''
                        SELECT 1 FROM sent_emails
                        WHERE email = ? AND reserved = 1 AND reserved_for != ?
                    ''', (email, server_ip))

                    already_reserved = c.fetchone() is not None

                    if not already_reserved:  # Email não está reservado para outro servidor
                        # Reservar o email para este servidor
                        c.execute('''
                            INSERT OR REPLACE INTO sent_emails (email, cpf, reserved, reserved_for)
                            VALUES (?, ?, 1, ?)
                        ''', (email, cpf, server_ip))

                        # Escrever no arquivo temporário
                        f.write(f"{line}\n")

                        reserved_count += 1

            conn.commit()
            conn.close()

            print(f"Reservados {reserved_count} emails para o servidor {server_ip}")
            return reserved_count

        except Exception as e:
            print(f"Erro ao reservar emails: {str(e)}")
            return 0

    def _get_unsent_emails(self, email_list_path, server_ip=None):
        """
        Obtém emails não enviados do arquivo ou do banco de dados

        Args:
            email_list_path: Caminho para o arquivo de emails
            server_ip: IP do servidor para o qual obter emails reservados

        Returns:
            tuple: (header, emails_data)
                header: Cabeçalho do arquivo
                emails_data: Lista de dicionários com dados dos emails
        """
        if email_list_path is not None and not os.path.exists(email_list_path):
            print(f"Arquivo de lista de emails não encontrado: {email_list_path}")
            return None, []

        try:
            # Se server_ip for None, retorna vazio
            if server_ip is None:
                print("Nenhum servidor especificado para obter emails")
                return None, []

            # Verifica se existe um arquivo temporário específico para este servidor
            temp_file = f"temp_emails_server_{server_ip.replace('.', '_')}.txt"

            if os.path.exists(temp_file):
                # Se existe arquivo temporário para este servidor, usa ele
                with open(temp_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()

                if not lines:
                    print(f"Arquivo temporário vazio para o servidor {server_ip}")
                    return None, []

                # Verificar se o arquivo é no formato simples (sem vírgulas)
                first_line = lines[0].strip()
                if ',' not in first_line:
                    print("Arquivo temporário no formato simples: um email por linha")
                    header = "email"
                    start_idx = 0  # Começar da primeira linha
                    email_idx = 0
                    cpf_idx = None
                else:
                    # Determinar formato do arquivo
                    has_header = ',' in first_line and any(field.lower() == 'email' for field in first_line.split(','))

                    # Se não tiver cabeçalho, assumimos formato: nome,email,cpf
                    if not has_header:
                        header = "nome,email,cpf"
                        start_idx = 0  # Começar da primeira linha
                    else:
                        header = first_line
                        start_idx = 1  # Pular o cabeçalho

                    header_fields = header.split(',')

                    # Verifica se o arquivo tem os campos necessários
                    email_idx = None
                    cpf_idx = None

                    for i, field in enumerate(header_fields):
                        if field.lower() == 'email':
                            email_idx = i
                        elif field.lower() in ['cpf', 'documento']:
                            cpf_idx = i

                    # Se não encontrou campo de email, tenta assumir posição padrão
                    if email_idx is None:
                        if len(header_fields) >= 2:
                            email_idx = 1  # Assume que email é o segundo campo
                        else:
                            print("Não foi possível identificar o campo de email")
                            return None, []

                # Processa as linhas de dados
                emails_data = []
                for line in lines[start_idx:]:  # Pula o cabeçalho se existir
                    if not line.strip():
                        continue

                    # Se o arquivo for no formato simples (sem vírgulas)
                    if ',' not in line and email_idx == 0:
                        email = line.strip().lower()
                        # Adiciona à lista de emails a enviar
                        email_data = {'email': email, 'cpf': ''}
                    else:
                        fields = line.strip().split(',')
                        if len(fields) <= email_idx:
                            continue  # Linha inválida

                        email = fields[email_idx].strip().lower()

                        # Adiciona à lista de emails a enviar
                        email_data = {'email': email}

                        # Adiciona CPF se disponível
                        if cpf_idx is not None and len(fields) > cpf_idx:
                            email_data['cpf'] = fields[cpf_idx].strip()

                    emails_data.append(email_data)

                print(f"Obtidos {len(emails_data)} emails do arquivo temporário para o servidor {server_ip}")
                return header, emails_data
            elif server_ip:
                # Se não existe arquivo temporário, verifica no banco de dados
                conn = sqlite3.connect(SENT_EMAILS_DB)
                c = conn.cursor()

                # Obter emails reservados para este servidor
                c.execute('''
                    SELECT email, cpf FROM sent_emails
                    WHERE reserved = 1 AND reserved_for = ?
                ''', (server_ip,))

                emails_data = []
                for row in c.fetchall():
                    emails_data.append({
                        'email': row[0],
                        'cpf': row[1] if row[1] else ''
                    })

                conn.close()

                if emails_data:
                    print(f"Obtidos {len(emails_data)} emails reservados para o servidor {server_ip}")
                    return "email,cpf", emails_data
                else:
                    print(f"Nenhum email reservado encontrado para o servidor {server_ip}")
                    return None, []
            else:
                # Se não existe arquivo temporário nem reservas no banco, retorna vazio
                print(f"Nenhum email reservado encontrado para o servidor {server_ip}")
                return None, []

        except Exception as e:
            print(f"Erro ao processar lista de emails: {e}")
            return None, []

    def check_server_status(self):
        """Verifica o status de todos os servidores Postfix"""
        if not self.servers:
            print("Nenhum servidor disponível")
            return False

        def check_single_server(session, server):
            try:
                url = f"http://{server['ip']}/postfix-status"
                response = session.get(url, timeout=30)
                if response.status_code == 200:
                    data = response.json()
                    print(f"Status do Postfix ({server['ip']}): {data.get('emailStatus', 'desconhecido')}")
                    return True
                else:
                    print(f"Erro ao verificar status do servidor {server['ip']}: {response.status_code}")
                    return False
            except Exception as e:
                print(f"Falha ao conectar com o servidor {server['ip']}: {str(e)}")
                return False

        with ThreadPoolExecutor() as executor:
            results = list(executor.map(
                check_single_server,
                self.sessions,
                self.servers
            ))

        active_servers = sum(results)
        print(f"\nResumo: {active_servers}/{len(self.servers)} servidores ativos")

        # Continua se pelo menos um servidor estiver ativo
        return active_servers > 0

    def upload_html(self, html_file_path):
        """Faz upload do arquivo HTML para todos servidores"""
        if not os.path.exists(html_file_path):
            print(f"Arquivo HTML não encontrado: {html_file_path}")
            return False

        if not self.servers:
            print("Nenhum servidor disponível")
            return False

        def upload_to_server(session, server):
            try:
                url = f"http://{server['ip']}/upload-html"
                with open(html_file_path, 'rb') as f:
                    files = {'html': (os.path.basename(html_file_path), f, 'text/html')}
                    response = session.post(
                        url,
                        files=files,
                        timeout=60
                    )

                    if response.status_code == 200:
                        data = response.json()
                        print(f"HTML enviado para {server['ip']}: {data.get('message', '')}")
                        return True
                    else:
                        print(f"Erro no upload para {server['ip']}: {response.status_code}")
                        print(f"Resposta: {response.text}")
                        return False
            except Exception as e:
                print(f"Falha no upload para {server['ip']}: {str(e)}")
                return False

        with ThreadPoolExecutor() as executor:
            results = list(executor.map(
                upload_to_server,
                self.sessions,
                self.servers
            ))

        # Retorna True se pelo menos um upload foi bem sucedido
        return any(results)

    def reset_email_index(self, email_list_path):
        """
        Reseta o índice de emails para começar do início do arquivo

        Args:
            email_list_path: Caminho para o arquivo de emails

        Returns:
            bool: True se o índice foi resetado com sucesso
        """
        try:
            index_file = f"{email_list_path}.index"
            if os.path.exists(index_file):
                os.remove(index_file)
                print(f"Índice de emails resetado para o arquivo: {email_list_path}")
            else:
                print(f"Nenhum índice encontrado para o arquivo: {email_list_path}")
            return True
        except Exception as e:
            print(f"Erro ao resetar índice de emails: {e}")
            return False

    def upload_email_list(self, email_list_path, emails_per_server=5000, reset_index=False):
        """
        Faz upload da lista de emails dividindo entre os servidores disponíveis
        Cada servidor recebe no máximo emails_per_server emails

        Args:
            email_list_path: Caminho para o arquivo de emails
            emails_per_server: Número máximo de emails por servidor
            reset_index: Se True, reseta o índice para começar do início do arquivo
        """
        # Resetar índice se solicitado
        if reset_index:
            self.reset_email_index(email_list_path)
        if not self.servers:
            print("Nenhum servidor disponível")
            return False

        temp_files = []
        success_count = 0

        try:
            # Para cada servidor, verificar se já tem emails reservados ou criar novos
            for i, server in enumerate(self.servers):
                server_ip = server['ip']
                temp_file = f"temp_emails_server_{server_ip.replace('.', '_')}.txt"

                # Verificar se o arquivo temporário existe
                if os.path.exists(temp_file):
                    print(f"Usando arquivo temporário existente para o servidor {server_ip}")
                    temp_files.append(temp_file)
                else:
                    # Se não existe, reservar novos emails
                    print(f"Reservando novos emails para o servidor {server_ip}")
                    emails_reservados = self.reserve_emails_for_server(
                        email_list_path,
                        server_ip,
                        emails_per_server
                    )

                    if emails_reservados > 0:
                        print(f"Reservados {emails_reservados} emails para o servidor {server_ip}")
                        if os.path.exists(temp_file):
                            temp_files.append(temp_file)
                        else:
                            print(f"Erro: Arquivo temporário não foi criado para {server_ip}")
                    else:
                        print(f"Não foi possível reservar emails para o servidor {server_ip}")

            if not temp_files:
                print("Nenhum email disponível para envio")
                return False

            # Faz upload paralelo para todos os servidores
            def upload_part(session, server):
                server_ip = server['ip']
                temp_file = f"temp_emails_server_{server_ip.replace('.', '_')}.txt"

                if not os.path.exists(temp_file):
                    print(f"Arquivo temporário não encontrado para {server_ip}: {temp_file}")
                    return False

                try:
                    url = f"http://{server['ip']}/upload-list"
                    with open(temp_file, 'rb') as f:
                        files = {'emails': (os.path.basename(temp_file), f, 'text/plain')}
                        response = session.post(
                            url,
                            files=files,
                            timeout=300
                        )

                        if response.status_code == 200:
                            data = response.json()
                            print(f"Lista enviada para {server['ip']}: {data.get('message', '')}")
                            print(f"Entradas: {data.get('details', {}).get('entries', 0)}")
                            return True
                        else:
                            print(f"Erro no upload para {server['ip']}: {response.status_code}")
                            print(f"Resposta: {response.text}")
                            return False
                except Exception as e:
                    print(f"Falha no upload para {server['ip']}: {str(e)}")
                    return False

            # Executar uploads em paralelo
            with ThreadPoolExecutor() as executor:
                futures = []
                for i, (session, server) in enumerate(zip(self.sessions, self.servers)):
                    futures.append(executor.submit(upload_part, session, server))

                # Processar resultados
                for future in futures:
                    if future.result():
                        success_count += 1

            return success_count > 0

        except Exception as e:
            print(f"Erro ao fazer upload das listas de emails: {str(e)}")
            return False

    def start_campaign(self, senders, subjects, max_retries=3, use_hostname=True):
        """Inicia a campanha em todos os servidores com os remetentes e assuntos especificados

        Args:
            senders: Lista de remetentes
            subjects: Lista de assuntos
            max_retries: Número máximo de tentativas
            use_hostname: Se True, substitui os remetentes por "Receita Master <hostname>"
        """
        if not self.servers:
            print("Nenhum servidor disponível")
            return []

        if not isinstance(senders, list) or len(senders) == 0:
            print("Forneça pelo menos um remetente")
            return []

        if not isinstance(subjects, list) or len(subjects) == 0:
            print("Forneça pelo menos um assunto")
            return []

        # Lista para armazenar os dados de cada campanha iniciada
        campaign_data = []

        def start_on_server(session, server):
            server_ip = server['ip']

            # Se use_hostname for True, usa apenas "Receita Master" como remetente
            # O Postfix adicionará automaticamente o hostname ao endereço
            if use_hostname:
                # Usamos apenas o nome sem o hostname para evitar duplicação
                server_senders = ["Receita Master"]
                print(f"Usando remetente personalizado para {server_ip}: {server_senders[0]}")
                print(f"O Postfix adicionará automaticamente o hostname ao endereço")
            else:
                server_senders = senders

            # Payload específico para este servidor
            payload = {
                "senders": server_senders,
                "subjects": subjects
            }

            for attempt in range(max_retries):
                try:
                    url = f"http://{server_ip}/start-campaign"
                    response = session.post(
                        url,
                        json=payload,
                        timeout=60
                    )

                    if response.status_code == 200:
                        data = response.json()
                        campaign_id = data.get('campaignId')
                        print(f"Campanha iniciada em {server_ip}! ID: {campaign_id}")
                        print(f"Detalhes: {json.dumps(data.get('details', {}), indent=2)}")

                        # Registrar no arquivo de log
                        campaign_start_time = datetime.now().isoformat()
                        with open('campaign_ids.log', 'a') as f:
                            f.write(f"{server_ip},{campaign_id},{campaign_start_time}\n")

                        # Obter emails reservados para este servidor e marcá-los como enviados
                        try:
                            _, emails_data = self._get_unsent_emails(None, server_ip=server_ip)
                            if emails_data:
                                self._mark_emails_as_sent(emails_data, campaign_id, server_ip)
                        except Exception as e:
                            print(f"Aviso: Não foi possível marcar emails como enviados: {e}")

                        return {
                            'server': server,
                            'campaign_id': campaign_id,
                            'start_time': campaign_start_time
                        }
                    else:
                        print(f"Erro ao iniciar campanha em {server_ip} (tentativa {attempt + 1}/{max_retries}): {response.status_code}")
                        print(f"Resposta: {response.text}")
                        if attempt < max_retries - 1:
                            time.sleep(5)  # Espera antes de tentar novamente
                except Exception as e:
                    print(f"Falha ao iniciar campanha em {server_ip} (tentativa {attempt + 1}/{max_retries}): {str(e)}")
                    if attempt < max_retries - 1:
                        time.sleep(5)  # Espera antes de tentar novamente

            print(f"Falha ao iniciar campanha em {server_ip} após {max_retries} tentativas")
            return None

        # Iniciar campanhas em todos os servidores
        with ThreadPoolExecutor() as executor:
            futures = []
            for session, server in zip(self.sessions, self.servers):
                futures.append(executor.submit(start_on_server, session, server))

            # Coletar resultados
            for future in futures:
                result = future.result()
                if result is not None:
                    campaign_data.append(result)

        # Register campaigns with the tracker if available
        try:
            from campaign_tracker import register_new_campaign
            register_new_campaign(campaign_data)
        except ImportError:
            # Campaign tracker not available, continue without it
            pass
        except Exception as e:
            print(f"Error registering campaigns with tracker: {e}")

        # Retorna apenas os dados das campanhas iniciadas com sucesso
        return campaign_data

    async def monitor_campaign_async(self, campaign_data=None, interval=5, log_failures_to=None, max_retries=3):
        """Monitora o progresso de campanhas em todos servidores de forma assíncrona"""
        import asyncio
        import aiohttp

        # Se nenhum dado for fornecido, tenta ler do arquivo de log
        if campaign_data is None:
            try:
                campaign_data = []
                with open('campaign_ids.log', 'r') as f:
                    for line in f:
                        if line.startswith('#'):
                            continue
                        parts = line.strip().split(',')
                        if len(parts) >= 2:
                            campaign_data.append({
                                'server': {'ip': parts[0]},
                                'campaign_id': parts[1],
                                'start_time': parts[2] if len(parts) > 2 else datetime.now().isoformat()
                            })
            except FileNotFoundError:
                print("Arquivo campaign_ids.log não encontrado")
                return False

        if not campaign_data:
            print("Nenhum dado de campanha disponível para monitoramento")
            return False

        print(f"\nMonitorando assincronamente {len(campaign_data)} campanhas...")
        print("Pressione Ctrl+C para parar o monitoramento\n")

        async def check_campaign_status(session, campaign):
            server_ip = campaign['server']['ip']
            campaign_id = campaign['campaign_id']

            for attempt in range(max_retries):
                try:
                    url = f"http://{server_ip}/campanha/{campaign_id}"
                    async with session.get(
                        url,
                        timeout=aiohttp.ClientTimeout(total=30)
                    ) as response:

                        if response.status == 200:
                            data = await response.json()
                            stats = data.get('estatisticas', {})
                            return {
                                'server_ip': server_ip,
                                'campaign_id': campaign_id,
                                'status': data.get('status', 'desconhecido'),
                                'sent': stats.get('enviados', 0),
                                'failed': stats.get('falhas', 0),
                                'pending': stats.get('pendentes', 0),
                                'total': stats.get('total', 0),
                                'failures': data.get('falhas_detalhes', [])
                            }
                        else:
                            print(f"Erro ao obter status da campanha {campaign_id} em {server_ip} (tentativa {attempt + 1}/{max_retries}): {response.status}")
                            if attempt < max_retries - 1:
                                await asyncio.sleep(5)
                except Exception as e:
                    print(f"Erro ao monitorar campanha {campaign_id} em {server_ip} (tentativa {attempt + 1}/{max_retries}): {str(e)}")
                    if attempt < max_retries - 1:
                        await asyncio.sleep(5)
            return None

        try:
            async with aiohttp.ClientSession(headers={
                'User-Agent': 'PostfixCampaignSender/1.0',
                'Accept': 'application/json'
            }) as session:

                all_completed = False

                while not all_completed:
                    tasks = []
                    for campaign in campaign_data:
                        tasks.append(check_campaign_status(session, campaign))

                    # Executa todas as verificações simultaneamente
                    results = await asyncio.gather(*tasks)

                    total_sent = 0
                    total_failed = 0
                    total_pending = 0
                    active_servers = 0
                    all_completed = True

                    for result in results:
                        if result is None:
                            continue

                        total_sent += result['sent']
                        total_failed += result['failed']
                        total_pending += result['pending']
                        active_servers += 1

                        # Se ainda há emails pendentes, não está completo
                        if result['pending'] > 0:
                            all_completed = False

                        # Exibir detalhes do servidor atual
                        print(f"\nServidor: {result['server_ip']}")
                        print(f"Campanha ID: {result['campaign_id']}")
                        print(f"Status: {result['status']}")
                        print(f"Enviados: {result['sent']} | Falhas: {result['failed']} | Pendentes: {result['pending']}")

                        if result['total'] > 0:
                            print(f"Progresso: {(result['sent']/result['total'])*100:.2f}%")

                        # Registrar falhas
                        if log_failures_to and result['failed'] > 0 and result['failures']:
                            with open(log_failures_to, 'a') as f:
                                for fail in result['failures']:
                                    f.write(f"{fail.get('email','unknown')},{fail.get('cpf','unknown')},{fail.get('motivo','Falha - Email retornou')}\n")

                    total_emails = total_sent + total_failed + total_pending
                    progress = (total_sent / total_emails) * 100 if total_emails > 0 else 0

                    print(f"\n{'='*50}")
                    print(f"Status geral ({active_servers}/{len(campaign_data)} servidores ativos):")
                    print(f"Progresso total: {progress:.2f}%")
                    print(f"Enviados: {total_sent} | Falhas: {total_failed} | Pendentes: {total_pending}")
                    print(f"Última atualização: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                    print(f"{'='*50}")

                    if all_completed:
                        print("\nTodas campanhas finalizadas!")
                        return True

                    await asyncio.sleep(interval)

        except KeyboardInterrupt:
            print("\nMonitoramento interrompido pelo usuário")
            return True
        except Exception as e:
            print(f"Erro no monitoramento: {str(e)}")
            return False

    def monitor_campaign(self, campaign_data=None, interval=5, log_failures_to=None, max_retries=3):
        """Wrapper síncrono para o monitoramento assíncrono"""
        import asyncio
        return asyncio.run(self.monitor_campaign_async(campaign_data, interval, log_failures_to, max_retries))

    def send_email_list_to_server(self, server_ip):
        """
        Envia a lista de emails para um servidor específico
        """
        temp_file = f"temp_emails_server_{server_ip.replace('.', '_')}.txt"

        if not os.path.exists(temp_file):
            print(f"Arquivo temporário não encontrado: {temp_file}")
            return False

        try:
            files = {
                'emails': ('emails.txt', open(temp_file, 'rb'), 'text/plain')
            }

            response = requests.post(
                f"http://{server_ip}:3000/upload-list",
                files=files
            )

            if response.status_code == 200:
                print(f"Lista enviada para {server_ip}: {response.json().get('message', '')}")
                return True
            else:
                print(f"Erro ao enviar lista para {server_ip}: {response.text}")
                return False

        except Exception as e:
            print(f"Erro ao enviar lista para {server_ip}: {str(e)}")
            return False

# Função para executar campanha completa
async def run_campaign(html_file, email_list, senders, subjects, emails_per_server=5000, reset_index=False):
    """
    Executa uma campanha completa: verifica servidores, faz upload de HTML e emails, inicia e monitora

    Args:
        html_file: Caminho para o arquivo HTML
        email_list: Caminho para a lista de emails
        senders: Lista de remetentes
        subjects: Lista de assuntos
        emails_per_server: Máximo de emails por servidor
        reset_index: Se True, reseta o índice para começar do início do arquivo

    Returns:
        bool: True se a campanha foi iniciada com sucesso
    """
    # Criar instância do sender com servidores ativos
    sender = PostfixCampaignSender()

    if not sender.servers:
        print("Nenhum servidor ativo encontrado")
        return False

    # Verificar status dos servidores
    if not sender.check_server_status():
        print("Nenhum servidor respondendo. Abortando campanha.")
        return False

    # Fazer upload do HTML
    print("\nEnviando arquivo HTML...")
    if not sender.upload_html(html_file):
        print("Falha ao enviar HTML. Abortando campanha.")
        return False

    # Fazer upload da lista de emails
    print("\nEnviando lista de emails...")
    if not sender.upload_email_list(email_list, emails_per_server, reset_index=reset_index):
        print("Falha ao enviar lista de emails. Abortando campanha.")
        return False

    # Iniciar a campanha
    print("\nIniciando campanha...")
    campaign_data = sender.start_campaign(senders, subjects)
    if not campaign_data:
        print("Falha ao iniciar campanha.")
        return False

    # Monitorar o progresso da campanha
    print("\nIniciando monitoramento das campanhas...")
    await sender.monitor_campaign_async(campaign_data, interval=5, log_failures_to="falhas.log")

    return True
