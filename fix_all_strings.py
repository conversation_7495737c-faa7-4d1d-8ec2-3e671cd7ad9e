#!/usr/bin/env python3
import asyncio
import asyncssh
import sys
from colorama import Fore, init

# Inicializar colorama
init(autoreset=True)

# Configurações
SSH_PORT = 22
SSH_USERNAME = "root"
SSH_PASSWORD = "eE5522892004@@e"
IP_TO_CHECK = "*************"  # IP da máquina a ser verificada

async def corrigir_servidor():
    try:
        print(Fore.YELLOW + f"🔄 Conectando via SSH a {IP_TO_CHECK}...")
        
        # Tentar conectar via SSH
        conn = await asyncssh.connect(
            IP_TO_CHECK, 
            port=SSH_PORT, 
            username=SSH_USERNAME, 
            password=SSH_PASSWORD, 
            known_hosts=None  # Ignora verificação de known_hosts
        )
        
        print(Fore.GREEN + f"✅ Conexão SSH estabelecida com {IP_TO_CHECK}")
        
        # Fazer backup do arquivo server.js
        print(Fore.YELLOW + "🔄 Fazendo backup do arquivo server.js...")
        await conn.run("docker exec api-enviador bash -c 'cp /app/server.js /app/server.js.bak.final'")
        print(Fore.GREEN + "✅ Backup criado em /app/server.js.bak.final")
        
        # Corrigir a linha 1470
        print(Fore.YELLOW + "🔄 Corrigindo a linha 1470...")
        await conn.run("docker exec api-enviador bash -c 'echo \"    logger.info(\\\"Servidor rodando em http://localhost:\\\" + PORT);\" > /tmp/line1470.txt'")
        await conn.run("docker exec api-enviador bash -c 'sed -i \"1470s/.*$/$(cat /tmp/line1470.txt)/\" /app/server.js'")
        
        # Verificar se a correção foi aplicada
        print(Fore.YELLOW + "🔄 Verificando se a correção foi aplicada...")
        result = await conn.run("docker exec api-enviador bash -c 'sed -n \"1470p\" /app/server.js'")
        print(Fore.CYAN + "=== Linha corrigida no arquivo server.js ===")
        print(result.stdout)
        
        # Corrigir todas as outras strings de template no arquivo
        print(Fore.YELLOW + "🔄 Corrigindo todas as outras strings de template no arquivo...")
        
        # Criar um script para encontrar todas as strings de template
        find_script = """#!/bin/bash
# Script para encontrar todas as strings de template no arquivo server.js

# Caminho do arquivo
FILE="/app/server.js"

# Encontrar todas as strings de template
grep -n "\`.*\${.*}\`" "$FILE" > /tmp/template_strings.txt

echo "Strings de template encontradas:"
cat /tmp/template_strings.txt
"""
        
        # Enviar o script para o contêiner
        await conn.run("echo '" + find_script + "' > /tmp/find_templates.sh")
        await conn.run("chmod +x /tmp/find_templates.sh")
        await conn.run("docker cp /tmp/find_templates.sh api-enviador:/tmp/find_templates.sh")
        
        # Executar o script no contêiner
        print(Fore.YELLOW + "🔄 Encontrando todas as strings de template...")
        result = await conn.run("docker exec api-enviador bash /tmp/find_templates.sh")
        print(Fore.CYAN + "=== Strings de template encontradas ===")
        print(result.stdout)
        
        # Criar um script para corrigir todas as strings de template
        fix_script = """#!/bin/bash
# Script para corrigir todas as strings de template no arquivo server.js

# Caminho do arquivo
FILE="/app/server.js"

# Fazer backup do arquivo original
cp "$FILE" "$FILE.bak.script2"

# Corrigir todas as strings de template
# Substituir todas as ocorrências de `texto ${var}` por "texto " + var
sed -i 's/\(`[^`]*\${[^}]*}\)`/"\1"/g' "$FILE"
sed -i 's/\(`\)\([^`]*\)\(${[^}]*}\)\([^`]*\)\(`\)/"\2" + \3 + "\4"/g' "$FILE"
sed -i 's/\(`\)\([^`]*\)\(${[^}]*}\)\([^`]*\)\(${[^}]*}\)\([^`]*\)\(`\)/"\2" + \3 + "\4" + \5 + "\6"/g' "$FILE"

# Corrigir casos específicos
sed -i 's/logger.info("\(`[^`]*\${[^}]*}\)`")/logger.info("\1")/g' "$FILE"
sed -i 's/logger.error("\(`[^`]*\${[^}]*}\)`")/logger.error("\1")/g' "$FILE"
sed -i 's/logger.warn("\(`[^`]*\${[^}]*}\)`")/logger.warn("\1")/g' "$FILE"
sed -i 's/logger.debug("\(`[^`]*\${[^}]*}\)`")/logger.debug("\1")/g' "$FILE"

# Corrigir a linha 1470 especificamente
sed -i '1470s/.*$/    logger.info("Servidor rodando em http:\/\/localhost:" + PORT);/' "$FILE"

# Corrigir a linha 1159 especificamente
sed -i '1159s/.*$/        logger.error("Erro na campanha " + req.params.id + ": " + error.message);/' "$FILE"

echo "Correções aplicadas com sucesso!"
"""
        
        # Enviar o script para o contêiner
        await conn.run("echo '" + fix_script + "' > /tmp/fix_all_templates.sh")
        await conn.run("chmod +x /tmp/fix_all_templates.sh")
        await conn.run("docker cp /tmp/fix_all_templates.sh api-enviador:/tmp/fix_all_templates.sh")
        
        # Executar o script no contêiner
        print(Fore.YELLOW + "🔄 Corrigindo todas as strings de template...")
        result = await conn.run("docker exec api-enviador bash /tmp/fix_all_templates.sh")
        print(Fore.CYAN + "=== Resultado da correção ===")
        print(result.stdout)
        
        # Tentar iniciar o servidor manualmente para verificar se os erros foram corrigidos
        print(Fore.YELLOW + "🔄 Tentando iniciar o servidor manualmente para verificar se os erros foram corrigidos...")
        result = await conn.run("docker exec api-enviador bash -c 'cd /app && node server.js' 2>&1 || echo 'Ainda há erros no servidor'")
        print(Fore.CYAN + "=== Resultado da Tentativa de Iniciar o Servidor ===")
        print(result.stdout)
        
        # Reiniciar o contêiner
        print(Fore.YELLOW + "🔄 Reiniciando o contêiner api-enviador...")
        await conn.run("docker restart api-enviador")
        print(Fore.GREEN + "✅ Contêiner reiniciado")
        
        # Aguardar um pouco para o serviço iniciar
        print(Fore.YELLOW + "🔄 Aguardando o serviço iniciar...")
        await asyncio.sleep(10)
        
        # Verificar status do contêiner
        print(Fore.YELLOW + "🔄 Verificando status do contêiner...")
        result = await conn.run("docker ps -a --filter name=api-enviador --format '{{.Names}} {{.Status}}'")
        print(Fore.CYAN + "=== Status do Contêiner api-enviador ===")
        print(result.stdout)
        
        # Verificar logs do contêiner
        print(Fore.YELLOW + "🔄 Verificando logs recentes do contêiner...")
        result = await conn.run("docker logs --tail 20 api-enviador")
        print(Fore.CYAN + "=== Logs Recentes do Contêiner api-enviador ===")
        print(result.stdout)
        
        # Fechar conexão SSH
        conn.close()
        print(Fore.GREEN + "✅ Correção concluída")
        
    except Exception as e:
        print(Fore.RED + f"❌ Erro: {e}")
        return False

async def main():
    await corrigir_servidor()

if __name__ == "__main__":
    asyncio.run(main())
