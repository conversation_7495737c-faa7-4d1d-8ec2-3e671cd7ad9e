import sqlite3

DB_NAME = "unified_system.db"

def initialize_database():
    with sqlite3.connect(DB_NAME) as conn:
        cursor = conn.cursor()
        cursor.executescript('''
            CREATE TABLE IF NOT EXISTS machines (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                provider TEXT NOT NULL,
                provider_token TEXT NOT NULL,
                ip_address TEXT NOT NULL UNIQUE,
                password TEXT NOT NULL,
                vm_code TEXT,
                configurada BOOLEAN DEFAULT 0,
                active BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            );

            CREATE TABLE IF NOT EXISTS campaigns (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                status TEXT DEFAULT 'pending',
                start_time DATETIME,
                end_time DATETIME,
                total_emails_sent INTEGER DEFAULT 0,
                total_emails_failed INTEGER DEFAULT 0
            );

            CREATE TABLE IF NOT EXISTS email_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                campaign_id INTEGER,
                machine_id INTEGER,
                email TEXT NOT NULL,
                status TEXT,
                sent_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (campaign_id) REFERENCES campaigns(id),
                FOREIGN KEY (machine_id) REFERENCES machines(id)
            );

            CREATE TABLE IF NOT EXISTS serversAtivosEnvio (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                nome TEXT NOT NULL,
                url TEXT NOT NULL UNIQUE,
                ativo BOOLEAN DEFAULT 1,
                status TEXT DEFAULT 'disponível',
                token TEXT NOT NULL
            );
        ''')
        conn.commit()

def store_machine_data(provider, ip_address, password, vm_code, token, configurada=0):
    with sqlite3.connect(DB_NAME) as conn:
        cursor = conn.cursor()
        cursor.execute('''
            INSERT OR IGNORE INTO machines (provider, ip_address, password, vm_code, provider_token, configurada)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (provider, ip_address, password, vm_code, token, configurada))
        conn.commit()

def mark_machine_configured(ip):
    with sqlite3.connect(DB_NAME) as conn:
        cursor = conn.cursor()
        cursor.execute("UPDATE machines SET configurada = 1 WHERE ip_address = ?", (ip,))
        conn.commit()

def is_machine_configured(ip):
    with sqlite3.connect(DB_NAME) as conn:
        cursor = conn.cursor()
        cursor.execute("SELECT configurada FROM machines WHERE ip_address = ?", (ip,))
        result = cursor.fetchone()
        return result is not None and result[0] == 1

def add_active_server(nome, url, token):
    with sqlite3.connect(DB_NAME) as conn:
        cursor = conn.cursor()
        cursor.execute('''
            INSERT OR IGNORE INTO serversAtivosEnvio (nome, url, token, status)
            VALUES (?, ?, ?, 'Entregue')
        ''', (nome, url, token))
        conn.commit()

def update_server_status(nome, status):
    with sqlite3.connect(DB_NAME) as conn:
        cursor = conn.cursor()
        cursor.execute('''
            UPDATE serversAtivosEnvio SET status = ? WHERE nome = ?
        ''', (status, nome))
        conn.commit()

def get_available_servers():
    with sqlite3.connect(DB_NAME) as conn:
        cursor = conn.cursor()
        cursor.execute('''
            SELECT nome, url FROM serversAtivosEnvio WHERE ativo = 1 AND status = 'Entregue'
        ''')
        return cursor.fetchall()

