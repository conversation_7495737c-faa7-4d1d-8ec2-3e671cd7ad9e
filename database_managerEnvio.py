import sqlite3
import logging
from datetime import datetime

DB_NAME = "linodes.db"

def initialize_database():
    with sqlite3.connect(DB_NAME) as conn:
        cursor = conn.cursor()
        cursor.executescript('''
            CREATE TABLE IF NOT EXISTS machines (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                provider TEXT NOT NULL,
                provider_token TEXT NOT NULL,
                ip_address TEXT NOT NULL UNIQUE,
                password TEXT NOT NULL,
                vm_code TEXT,
                configurada BOOLEAN DEFAULT 0,
                active BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            );

            CREATE TABLE IF NOT EXISTS campaigns (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                status TEXT DEFAULT 'pending',
                start_time DATETIME,
                end_time DATETIME,
                total_emails_sent INTEGER DEFAULT 0,
                total_emails_failed INTEGER DEFAULT 0
            );

            CREATE TABLE IF NOT EXISTS email_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                campaign_id INTEGER,
                machine_id INTEGER,
                email TEXT NOT NULL,
                status TEXT,
                sent_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (campaign_id) REFERENCES campaigns(id),
                FOREIGN KEY (machine_id) REFERENCES machines(id)
            );

            CREATE TABLE IF NOT EXISTS serversAtivos (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                linode_id INTEGER,
                label TEXT NOT NULL,
                ip TEXT,
                url TEXT NOT NULL UNIQUE,
                status INTEGER DEFAULT 1,
                email_status TEXT DEFAULT 'Entregue',
                token TEXT NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                ativo BOOLEAN DEFAULT 1
            );
        ''')

        # Check if linode_id column exists in serversAtivos table
        cursor.execute("PRAGMA table_info(serversAtivos)")
        columns = [column[1] for column in cursor.fetchall()]

        # Add missing columns if needed
        if 'linode_id' not in columns:
            cursor.execute("ALTER TABLE serversAtivos ADD COLUMN linode_id INTEGER")
            logging.info("Added linode_id column to serversAtivos table")

        if 'ip' not in columns:
            cursor.execute("ALTER TABLE serversAtivos ADD COLUMN ip TEXT")
            logging.info("Added ip column to serversAtivos table")

        if 'email_status' not in columns:
            cursor.execute("ALTER TABLE serversAtivos ADD COLUMN email_status TEXT DEFAULT 'Entregue'")
            logging.info("Added email_status column to serversAtivos table")

        if 'created_at' not in columns:
            cursor.execute("ALTER TABLE serversAtivos ADD COLUMN created_at DATETIME DEFAULT CURRENT_TIMESTAMP")
            logging.info("Added created_at column to serversAtivos table")

        conn.commit()

def store_machine_data(provider, ip_address, password, vm_code, token, configurada=0):
    with sqlite3.connect(DB_NAME) as conn:
        cursor = conn.cursor()
        cursor.execute('''
            INSERT OR IGNORE INTO machines (provider, ip_address, password, vm_code, provider_token, configurada)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (provider, ip_address, password, vm_code, token, configurada))
        conn.commit()

def mark_machine_configured(ip):
    with sqlite3.connect(DB_NAME) as conn:
        cursor = conn.cursor()
        cursor.execute("UPDATE machines SET configurada = 1 WHERE ip_address = ?", (ip,))
        conn.commit()

def is_machine_configured(ip):
    with sqlite3.connect(DB_NAME) as conn:
        cursor = conn.cursor()
        cursor.execute("SELECT configurada FROM machines WHERE ip_address = ?", (ip,))
        result = cursor.fetchone()
        return result is not None and result[0] == 1

def add_active_server(nome, url, token, status='Entregue', linode_id=None):
    with sqlite3.connect(DB_NAME) as conn:
        cursor = conn.cursor()
        cursor.execute('''
            INSERT OR IGNORE INTO serversAtivos (label, url, token, status, linode_id)
            VALUES (?, ?, ?, ?, ?)
        ''', (nome, url, token, status, linode_id))
        conn.commit()

def update_server_status(server_id, status=None, email_status=None):
    with sqlite3.connect(DB_NAME) as conn:
        cursor = conn.cursor()

        # Build the query dynamically based on what needs to be updated
        query_parts = []
        params = []

        if status is not None:
            query_parts.append("status = ?")
            params.append(status)

        if email_status is not None:
            query_parts.append("email_status = ?")
            params.append(email_status)

        if not query_parts:
            return  # Nothing to update

        query = f"UPDATE serversAtivos SET {', '.join(query_parts)} WHERE id = ?"
        params.append(server_id)

        cursor.execute(query, params)
        conn.commit()

def get_available_servers():
    with sqlite3.connect(DB_NAME) as conn:
        cursor = conn.cursor()

        # Check if ativo column exists
        cursor.execute("PRAGMA table_info(serversAtivos)")
        columns = [column[1] for column in cursor.fetchall()]

        if 'ativo' in columns:
            cursor.execute('''
                SELECT id, linode_id, label, ip, url, status, email_status, token, created_at
                FROM serversAtivos WHERE ativo = 1 AND status = 1
            ''')
        else:
            cursor.execute('''
                SELECT id, linode_id, label, ip, url, status, email_status, token, created_at
                FROM serversAtivos WHERE status = 1
            ''')

        return cursor.fetchall()

def create_campaign(name):
    """
    Cria um novo registro de campanha no banco de dados.

    Args:
        name: Nome da campanha

    Returns:
        int: ID da campanha criada
    """
    try:
        #conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("""
            INSERT INTO campaigns (name, status, start_time)
            VALUES (?, ?, datetime('now'))
        """, (name, 'running'))

        campaign_id = cursor.lastrowid
        conn.commit()
        conn.close()

        logging.info(f"Campanha criada no banco de dados. ID: {campaign_id}")
        return campaign_id
    except Exception as e:
        logging.error(f"Erro ao criar campanha no banco de dados: {str(e)}")
        return None

def update_campaign_status(campaign_id, status, total_sent=None, total_failed=None):
    """
    Atualiza o status de uma campanha existente.

    Args:
        campaign_id: ID da campanha
        status: Novo status (running, completed, failed, interrupted)
        total_sent: Total de emails enviados com sucesso
        total_failed: Total de emails com falha
    """
    if not campaign_id:
        return

    try:
        #conn = get_db_connection()
        cursor = conn.cursor()

        params = [status, campaign_id]
        query = "UPDATE campaigns SET status = ?"

        # Se for status final, adicionar timestamp de fim
        if status in ['completed', 'failed', 'interrupted']:
            query += ", end_time = datetime('now')"

        # Atualizar contadores, se fornecidos
        if total_sent is not None:
            query += ", total_emails_sent = ?"
            params.insert(1, total_sent)

        if total_failed is not None:
            query += ", total_emails_failed = ?"
            params.insert(2 if total_sent is None else 2, total_failed)

        query += " WHERE id = ?"

        cursor.execute(query, params)
        conn.commit()
        conn.close()

        logging.info(f"Status da campanha {campaign_id} atualizado para: {status}")
    except Exception as e:
        logging.error(f"Erro ao atualizar status da campanha: {str(e)}")

def update_campaign_stats(campaign_id, total_sent=None, total_failed=None):
    """
    Atualiza apenas as estatísticas de uma campanha sem alterar seu status.

    Args:
        campaign_id: ID da campanha
        total_sent: Total de emails enviados com sucesso
        total_failed: Total de emails com falha
    """
    if not campaign_id:
        return

    try:
        #conn = get_db_connection()
        cursor = conn.cursor()

        params = []
        query = "UPDATE campaigns SET"

        if total_sent is not None:
            query += " total_emails_sent = ?"
            params.append(total_sent)

        if total_failed is not None:
            if params:
                query += ","
            query += " total_emails_failed = ?"
            params.append(total_failed)

        if not params:
            return  # Nada para atualizar

        query += " WHERE id = ?"
        params.append(campaign_id)

        cursor.execute(query, params)
        conn.commit()
        conn.close()

        logging.debug(f"Estatísticas da campanha {campaign_id} atualizadas. Enviados: {total_sent}, Falhas: {total_failed}")
    except Exception as e:
        logging.error(f"Erro ao atualizar estatísticas da campanha: {str(e)}")

class VPSManager:
    def __init__(self, db_path='vps_database.db'):
        self.db_path = db_path
        self.logger = logging.getLogger()
        self._initialize_db()

    def _initialize_db(self):
        """Inicializa o banco de dados e cria as tabelas necessárias"""
        with self.get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.executescript('''
                CREATE TABLE IF NOT EXISTS vps (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    vm_code TEXT UNIQUE,
                    token_used TEXT,
                    status TEXT,
                    ip_address TEXT,
                    username TEXT,
                    password TEXT,
                    created_at DATETIME,
                    updated_at DATETIME,
                    error_time DATETIME
                );

                CREATE TABLE IF NOT EXISTS accounts (
                    token TEXT PRIMARY KEY,
                    email TEXT,
                    username TEXT,
                    trust_level INTEGER,
                    credit_balance REAL,
                    cost_rate_month REAL,
                    cost_rate_day REAL,
                    cost_rate_hour REAL,
                    last_updated DATETIME
                );

                CREATE TABLE IF NOT EXISTS sync_info (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    last_sync DATETIME
                );
            ''')

            # Verificar se a coluna error_time existe e adicionar se não existir
            cursor.execute("PRAGMA table_info(vps)")
            columns = [column[1] for column in cursor.fetchall()]
            if 'error_time' not in columns:
                cursor.execute("ALTER TABLE vps ADD COLUMN error_time DATETIME")
                self.logger.info("Coluna error_time adicionada à tabela vps")

            conn.commit()

    def get_db_connection(self):
        """Retorna uma conexão com o banco de dados"""
        return sqlite3.connect(self.db_path)

    def add_vps(self, vm_code, token_used, status='CREATING', ip_address=None, username=None, password=None):
        """Adiciona uma nova VPS ao banco de dados com status normalizado"""
        status = status.upper()  # Garante normalização
        with self.get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT OR IGNORE INTO vps (vm_code, token_used, status, ip_address, username, password, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (vm_code, token_used, status, ip_address, username, password, datetime.now(), datetime.now()))
            conn.commit()

    def update_vps_status(self, vm_code, status, ip_address=None, username=None, password=None):
        """Atualiza o status de uma VPS e registra o horário de erro quando aplicável"""
        try:
            status = str(status).upper()
            now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            # Condições especiais para ERROR
            error_time_param = None
            if status == 'ERROR':
                error_time_param = now

            with self.get_db_connection() as conn:
                # Conversão segura de valores
                if error_time_param:
                    # Se estiver mudando para ERROR, registrar o timestamp do erro
                    params = (
                        status,
                        str(ip_address) if ip_address else None,
                        str(username) if username else 'root',
                        str(password) if password else None,
                        now,  # updated_at
                        error_time_param,  # error_time
                        str(vm_code)
                    )

                    conn.execute('''
                        UPDATE vps
                        SET status = ?,
                            ip_address = ?,
                            username = ?,
                            password = ?,
                            updated_at = ?,
                            error_time = ?
                        WHERE vm_code = ?
                    ''', params)
                else:
                    # Para outros status, atualizar normalmente e limpar error_time
                    params = (
                        status,
                        str(ip_address) if ip_address else None,
                        str(username) if username else 'root',
                        str(password) if password else None,
                        now,  # updated_at
                        str(vm_code)
                    )

                    conn.execute('''
                        UPDATE vps
                        SET status = ?,
                            ip_address = ?,
                            username = ?,
                            password = ?,
                            updated_at = ?,
                            error_time = NULL
                        WHERE vm_code = ?
                    ''', params)

                conn.commit()
                self.logger.debug(f"VM {vm_code} status atualizado para {status}")

        except Exception as e:
            self.logger.error(f"Database error in update_vps_status: {str(e)}")
            raise

    def get_vps_by_vm_code(self, vm_code):
        """Obtém informações de uma VPS pelo código"""
        with self.get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM vps WHERE vm_code = ?', (vm_code,))
            return cursor.fetchone()

    def get_vps_by_status(self, status):
        """Obtém todas as VPS com um status específico ou lista de status"""
        with self.get_db_connection() as conn:
            cursor = conn.cursor()

            # Converter para lista se for único valor
            if isinstance(status, str):
                status = [status.upper()]
            else:
                status = [s.upper() for s in status]

            # Criar placeholders para a cláusula IN
            placeholders = ','.join(['?'] * len(status))

            cursor.execute(
                f'SELECT * FROM vps WHERE status IN ({placeholders})',
                tuple(status)
            )
            return cursor.fetchall()

    def get_all_vps(self):
        """Obtém todas as VPS do banco de dados"""
        with self.get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM vps')
            return cursor.fetchall()

    def delete_vps(self, vm_code):
        """Remove uma VPS do banco de dados com log detalhado"""
        with self.get_db_connection() as conn:
            cursor = conn.cursor()

            # Log antes da remoção
            cursor.execute('SELECT * FROM vps WHERE vm_code = ?', (vm_code,))
            vps_data = cursor.fetchone()
            if vps_data:
                print(f"🗑️ Removendo VPS: {vps_data}")

            cursor.execute('DELETE FROM vps WHERE vm_code = ?', (vm_code,))
            conn.commit()

    def update_account_info(self, token, email, username, trust_level, credit_balance,
                          cost_rate_month, cost_rate_day, cost_rate_hour):
        """Atualiza informações de uma conta"""
        with self.get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT OR REPLACE INTO accounts
                (token, email, username, trust_level, credit_balance,
                 cost_rate_month, cost_rate_day, cost_rate_hour, last_updated)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (token, email, username, trust_level, credit_balance,
                  cost_rate_month, cost_rate_day, cost_rate_hour, datetime.now()))
            conn.commit()

    def get_account_info(self, token):
        """Obtém informações de uma conta pelo token"""
        with self.get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM accounts WHERE token = ?', (token,))
            return cursor.fetchone()

    def update_last_sync(self):
        """Atualiza o timestamp da última sincronização"""
        with self.get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT OR REPLACE INTO sync_info (id, last_sync)
                VALUES (1, ?)
            ''', (datetime.now(),))
            conn.commit()

    def get_last_sync(self):
        """Obtém o timestamp da última sincronização"""
        with self.get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT last_sync FROM sync_info WHERE id = 1')
            result = cursor.fetchone()
            return datetime.strptime(result[0], '%Y-%m-%d %H:%M:%S.%f') if result else None

    def get_all_vps_by_token(self, token):
        """Retorna todas as VPS associadas a um token específico"""
        with self.get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM vps WHERE token_used = ?', (token,))
            return cursor.fetchall()

    def update_vps_credentials(self, vm_code, ip_address=None, username=None, password=None):
        """Atualiza apenas as credenciais da VPS sem alterar o status"""
        with self.get_db_connection() as conn:
            cursor = conn.cursor()
            update_fields = []
            params = []

            if ip_address:
                update_fields.append("ip_address = ?")
                params.append(ip_address)
            if username:
                update_fields.append("username = ?")
                params.append(username)
            if password:
                update_fields.append("password = ?")
                params.append(password)

            if update_fields:
                query = f"UPDATE vps SET {', '.join(update_fields)} WHERE vm_code = ?"
                params.append(vm_code)
                cursor.execute(query, params)
                conn.commit()

    def get_vps_error_time(self, vm_code):
        """Obtém o timestamp de quando uma VM entrou em estado de erro"""
        try:
            with self.get_db_connection() as conn:
                cursor = conn.execute(
                    "SELECT error_time FROM vps WHERE vm_code = ?",
                    (vm_code,)
                )
                result = cursor.fetchone()
                if result and result[0]:
                    try:
                        return datetime.strptime(result[0], '%Y-%m-%d %H:%M:%S')
                    except ValueError:
                        return None
                return None
        except Exception as e:
            self.logger.error(f"Database error in get_vps_error_time: {str(e)}")
            return None

# Add more functions as needed for campaigns and email logs

def import_server_data(data_lines):
    """
    Import server data from a formatted text with tab-separated values.
    Expected format:
    id  linode_id  label  ip  url  status  email_status  token  created_at

    Args:
        data_lines (str): Tab-separated data lines

    Returns:
        int: Number of records imported
    """
    with sqlite3.connect(DB_NAME) as conn:
        cursor = conn.cursor()

        # First clear the existing table to avoid duplicates
        cursor.execute("DELETE FROM serversAtivos")

        count = 0
        lines = data_lines.strip().split('\n')

        # Skip header line if present
        if lines and 'id' in lines[0] and 'linode_id' in lines[0]:
            lines = lines[1:]

        for line in lines:
            if not line.strip():
                continue

            # Split by tabs and strip whitespace
            fields = [field.strip() for field in line.split('\t')]

            if len(fields) < 8:
                logging.warning(f"Skipping invalid line: {line}")
                continue

            try:
                # Extract fields
                id_val = int(fields[0]) if fields[0].isdigit() else None
                linode_id = int(fields[1]) if fields[1].isdigit() else None
                label = fields[2]
                ip = fields[3]
                url = fields[4]
                status = int(fields[5]) if fields[5].isdigit() else 1
                email_status = fields[6]
                token = fields[7]

                # Handle created_at if present
                created_at = fields[8] if len(fields) > 8 else datetime.now().strftime('%Y-%m-%d %H:%M:%S')

                # Insert the record
                cursor.execute('''
                    INSERT INTO serversAtivos
                    (id, linode_id, label, ip, url, status, email_status, token, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (id_val, linode_id, label, ip, url, status, email_status, token, created_at))

                count += 1

            except Exception as e:
                logging.error(f"Error importing line: {line}. Error: {str(e)}")

        conn.commit()
        return count

def get_server_by_id(server_id):
    """Get server details by ID"""
    with sqlite3.connect(DB_NAME) as conn:
        cursor = conn.cursor()
        cursor.execute('''
            SELECT id, linode_id, label, ip, url, status, email_status, token, created_at
            FROM serversAtivos WHERE id = ?
        ''', (server_id,))
        return cursor.fetchone()

def get_server_by_linode_id(linode_id):
    """Get server details by Linode ID"""
    with sqlite3.connect(DB_NAME) as conn:
        cursor = conn.cursor()
        cursor.execute('''
            SELECT id, linode_id, label, ip, url, status, email_status, token, created_at
            FROM serversAtivos WHERE linode_id = ?
        ''', (linode_id,))
        return cursor.fetchone()

# Create an instance of VPSManager
vps_manager = VPSManager()

# Use the instance to call get_db_connection
conn = vps_manager.get_db_connection()

def example_import_data():
    """Example function to demonstrate how to import server data"""
    sample_data = """id	linode_id	label	ip	url	status	email_status	token	created_at
346	76110265	ubuntu-f9bed476-37	**************	http://**************	1	Entregue	xI9M9jQkvxQ0QmqUJZ6lNdn1glKt3TnHcKOaKIel6zYhyFjlT9M6gxsYSqfx9NN5	2025-04-29 21:37:53
347	76110462	ubuntu-e36c9439-47	***************	http://***************	1	Entregue	ejzH0EBQ7LzqGiW5dighrr20q1PxpnA6heUvq2xZIZHCvo1POD1snmeQWKFjhF1K	2025-04-29 21:38:37
348	76110282	ubuntu-21e401de-38	**************	http://**************	1	Entregue	9hhaI3lTMT0gzSz0J3FOINZTpzSqXIkGX6TVGVGEjglss5vgeRm2ReHMadS01z8D	2025-04-29 21:39:35"""

    # Import the data
    count = import_server_data(sample_data)
    print(f"Imported {count} server records")

    # Retrieve and display the imported data
    servers = get_available_servers()
    for server in servers:
        print(f"Server ID: {server[0]}, Linode ID: {server[1]}, Label: {server[2]}, IP: {server[3]}, Status: {server[5]}")

    return count

def import_from_clipboard(data_text):
    """
    Import server data directly from clipboard text

    Args:
        data_text (str): Tab-separated data copied from clipboard

    Returns:
        int: Number of records imported
    """
    try:
        # Clean up the data - remove any extra whitespace and normalize line endings
        cleaned_data = '\n'.join([line.strip() for line in data_text.splitlines() if line.strip()])

        # Import the data
        count = import_server_data(cleaned_data)
        print(f"Successfully imported {count} server records from clipboard data")
        return count
    except Exception as e:
        logging.error(f"Error importing data from clipboard: {str(e)}")
        print(f"Error importing data: {str(e)}")
        return 0

# Uncomment the line below to run the example
# example_import_data()

def import_servers_from_file(file_path):
    """
    Import server data from a tab-separated file

    Args:
        file_path (str): Path to the file containing tab-separated server data

    Returns:
        int: Number of records imported
    """
    try:
        with open(file_path, 'r') as f:
            data = f.read()
        return import_server_data(data)
    except Exception as e:
        logging.error(f"Error importing servers from file {file_path}: {str(e)}")
        return 0

def export_servers_to_file(file_path):
    """
    Export server data to a tab-separated file

    Args:
        file_path (str): Path to save the exported data

    Returns:
        int: Number of records exported
    """
    try:
        servers = get_available_servers()

        with open(file_path, 'w') as f:
            # Write header
            f.write("id\tlinode_id\tlabel\tip\turl\tstatus\temail_status\ttoken\tcreated_at\n")

            # Write data
            for server in servers:
                # Format each field properly
                id_val = str(server[0]) if server[0] is not None else ""
                linode_id = str(server[1]) if server[1] is not None else ""
                label = server[2] if server[2] is not None else ""
                ip = server[3] if server[3] is not None else ""
                url = server[4] if server[4] is not None else ""
                status = str(server[5]) if server[5] is not None else "1"
                email_status = server[6] if server[6] is not None else "Entregue"
                token = server[7] if server[7] is not None else ""
                created_at = server[8] if server[8] is not None else ""

                # Write the line
                f.write(f"{id_val}\t{linode_id}\t{label}\t{ip}\t{url}\t{status}\t{email_status}\t{token}\t{created_at}\n")

        return len(servers)
    except Exception as e:
        logging.error(f"Error exporting servers to file {file_path}: {str(e)}")
        return 0

if __name__ == "__main__":
    import sys

    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    # Initialize the database
    initialize_database()

    if len(sys.argv) > 1:
        command = sys.argv[1].lower()

        if command == "import" and len(sys.argv) > 2:
            # Import from file
            file_path = sys.argv[2]
            count = import_servers_from_file(file_path)
            print(f"Imported {count} server records from {file_path}")

        elif command == "export" and len(sys.argv) > 2:
            # Export to file
            file_path = sys.argv[2]
            count = export_servers_to_file(file_path)
            print(f"Exported {count} server records to {file_path}")

        elif command == "list":
            # List all servers
            servers = get_available_servers()
            print(f"Found {len(servers)} active servers:")
            print("ID\tLinode ID\tLabel\tIP\tURL\tStatus\tEmail Status")
            for server in servers:
                print(f"{server[0]}\t{server[1]}\t{server[2]}\t{server[3]}\t{server[4]}\t{server[5]}\t{server[6]}")

        elif command == "paste" and len(sys.argv) > 2:
            # Import from pasted data in a file
            file_path = sys.argv[2]
            try:
                with open(file_path, 'r') as f:
                    data = f.read()
                count = import_from_clipboard(data)
                print(f"Imported {count} server records from pasted data in {file_path}")
            except Exception as e:
                print(f"Error reading paste data file: {str(e)}")

        else:
            print("Usage:")
            print("  python database_managerEnvio.py import <file_path>  - Import servers from file")
            print("  python database_managerEnvio.py export <file_path>  - Export servers to file")
            print("  python database_managerEnvio.py list                - List all active servers")
            print("  python database_managerEnvio.py paste <file_path>   - Import from pasted data in file")
    else:
        print("Usage:")
        print("  python database_managerEnvio.py import <file_path>  - Import servers from file")
        print("  python database_managerEnvio.py export <file_path>  - Export servers to file")
        print("  python database_managerEnvio.py list                - List all active servers")
        print("  python database_managerEnvio.py paste <file_path>   - Import from pasted data in file")