#!/usr/bin/env python3
import asyncio
import asyncssh
import sys
from colorama import Fore, init

# Inicializar colorama
init(autoreset=True)

# Configurações
SSH_PORT = 22
SSH_USERNAME = "root"
SSH_PASSWORD = "eE5522892004@@e"
IP_TO_CHECK = "*************"  # IP da máquina a ser verificada

async def verificar_servidor():
    try:
        print(Fore.YELLOW + f"🔄 Conectando via SSH a {IP_TO_CHECK}...")

        # Tentar conectar via SSH
        conn = await asyncssh.connect(
            IP_TO_CHECK,
            port=SSH_PORT,
            username=SSH_USERNAME,
            password=SSH_PASSWORD,
            known_hosts=None  # Ignora verificação de known_hosts
        )

        print(Fore.GREEN + f"✅ Conexão SSH estabelecida com {IP_TO_CHECK}")

        # Verificar status do Docker
        print(Fore.YELLOW + "🔄 Verificando status do Docker...")
        result = await conn.run("docker ps -a")
        print(Fore.CYAN + "=== Status do Docker ===")
        print(result.stdout)

        # Verificar status do contêiner api-enviador
        print(Fore.YELLOW + "🔄 Verificando status do contêiner api-enviador...")
        result = await conn.run("docker ps -a --filter name=api-enviador --format '{{.Names}} {{.Status}}'")
        print(Fore.CYAN + "=== Status do Contêiner api-enviador ===")
        print(result.stdout)

        # Verificar logs do contêiner
        print(Fore.YELLOW + "🔄 Verificando logs recentes do contêiner api-enviador...")
        result = await conn.run("docker logs --tail 50 api-enviador")
        print(Fore.CYAN + "=== Logs Recentes do Contêiner api-enviador ===")
        print(result.stdout)

        # Tentar iniciar o servidor manualmente para ver o erro
        print(Fore.YELLOW + "🔄 Tentando iniciar o servidor manualmente para ver o erro...")
        result = await conn.run("docker exec api-enviador bash -c 'cd /app && node server.js' 2>&1 || echo 'Erro ao iniciar o servidor'")
        print(Fore.CYAN + "=== Resultado da Tentativa de Iniciar o Servidor ===")
        print(result.stdout)

        # Verificar o arquivo server.js
        print(Fore.YELLOW + "🔄 Verificando o arquivo server.js (trecho com formattedSender)...")
        result = await conn.run("docker exec api-enviador cat /app/server.js | grep -A 10 'formattedSender ='")
        print(Fore.CYAN + "=== Conteúdo do arquivo server.js (trecho com formattedSender) ===")
        print(result.stdout)

        # Verificar a linha com erro
        print(Fore.YELLOW + "🔄 Verificando a linha com erro (linha 1399)...")
        result = await conn.run("docker exec api-enviador bash -c 'grep -n \"Rate limited by Hotmail\" /app/server.js'")
        print(Fore.CYAN + "=== Linha com erro no arquivo server.js ===")
        print(result.stdout)

        # Verificar o contexto da linha com erro
        print(Fore.YELLOW + "🔄 Verificando o contexto da linha com erro...")
        result = await conn.run("docker exec api-enviador bash -c 'grep -A 5 -B 5 \"Rate limited by Hotmail\" /app/server.js'")
        print(Fore.CYAN + "=== Contexto da linha com erro ===")
        print(result.stdout)

        # Verificar se o servidor está respondendo
        print(Fore.YELLOW + "🔄 Verificando se o servidor está respondendo...")
        result = await conn.run("docker exec api-enviador curl -s http://localhost:3000/health || echo 'Servidor não está respondendo'")
        print(Fore.CYAN + "=== Resposta do Servidor ===")
        print(result.stdout)

        # Verificar portas em uso
        print(Fore.YELLOW + "🔄 Verificando portas em uso...")
        result = await conn.run("netstat -tulpn | grep LISTEN")
        print(Fore.CYAN + "=== Portas em Uso ===")
        print(result.stdout)

        # Fechar conexão SSH
        conn.close()
        print(Fore.GREEN + "✅ Verificação concluída")

    except Exception as e:
        print(Fore.RED + f"❌ Erro: {e}")
        return False

async def main():
    await verificar_servidor()

if __name__ == "__main__":
    asyncio.run(main())
