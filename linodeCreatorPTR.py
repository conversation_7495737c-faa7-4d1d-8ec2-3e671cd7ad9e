import asyncio
import asyncssh
import requests
import sqlite3
import time
import aiohttp
import json
import os
import uuid
import random

# Configurações
TOKEN = "0376615bea80c4e54ff670e4d4392964668b3f9f7dece0ae7ace72eef85564ce"
PROXY = "*********************************************************"
REGIAO = "us-east"
TIPO = "g6-nanode-1"
ROOT_PASS = "eE5522892004@@e"  # Troque para uma senha segura
DEPLOY_DIR = "enviador" # Caminho do diretório com os scripts de deploy

proxies = {
    "http": PROXY,
    "https": PROXY,
}

headers = {
    "Content-Type": "application/json",
    "Authorization": f"Bearer {TOKEN}"
}

DB_PATH = "linodes.db"

class DatabaseManager:
    _instance = None
    _connection = None
    _lock = asyncio.Lock()

    @classmethod
    def get_instance(cls):
        if cls._instance is None:
            cls._instance = DatabaseManager()
        return cls._instance

    def __init__(self):
        if self._connection is None:
            self._connection = sqlite3.connect(DB_PATH, timeout=20)
            # Habilitar foreign keys
            self._connection.execute("PRAGMA foreign_keys = ON")

    def get_connection(self):
        if not self._connection:
            self._connection = sqlite3.connect(DB_PATH, timeout=20)
        return self._connection

    def close(self):
        if self._connection:
            self._connection.close()
            self._connection = None

    async def execute(self, query, params=None):
        async with self._lock:
            cursor = self.get_connection().cursor()
            try:
                if params:
                    cursor.execute(query, params)
                else:
                    cursor.execute(query)
                self._connection.commit()
                return cursor
            except sqlite3.Error as e:
                print(f"Erro na execução da query: {e}")
                raise

    async def fetch_one(self, query, params=None):
        cursor = await self.execute(query, params)
        return cursor.fetchone()

    async def fetch_all(self, query, params=None):
        cursor = await self.execute(query, params)
        return cursor.fetchall()

# --- Banco de dados ---

def gerar_token(tamanho=64):
    """Gera um token aleatório para identificar o servidor."""
    import random
    import string
    caracteres = string.ascii_letters + string.digits
    return ''.join(random.choice(caracteres) for _ in range(tamanho))

def init_db():
    conn = sqlite3.connect(DB_PATH)
    c = conn.cursor()

    # Criar tabela linodes se não existir
    c.execute('''
        CREATE TABLE IF NOT EXISTS linodes (
            id INTEGER PRIMARY KEY,
            label TEXT,
            status TEXT,
            ipv4 TEXT,
            created TEXT,
            updated TEXT,
            docker_deployed INTEGER DEFAULT 0,
            email_status TEXT
        )
    ''')

    # Criar tabela serversAtivos se não existir
    c.execute('''
        CREATE TABLE IF NOT EXISTS serversAtivos (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            linode_id INTEGER,
            label TEXT,
            ip TEXT,
            url TEXT,
            status INTEGER DEFAULT 1,
            email_status TEXT,
            token TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (linode_id) REFERENCES linodes(id)
        )
    ''')

    # Verificar se a coluna email_status já existe na tabela linodes
    cursor = conn.execute('PRAGMA table_info(linodes)')
    colunas = [info[1] for info in cursor.fetchall()]

    # Adicionar coluna email_status se não existir
    if 'email_status' not in colunas:
        try:
            c.execute('ALTER TABLE linodes ADD COLUMN email_status TEXT')
            print("Coluna email_status adicionada à tabela linodes")
        except sqlite3.OperationalError:
            # Coluna já existe
            pass

    conn.commit()
    conn.close()

async def salvar_ou_atualizar_linode(linode):
    db = DatabaseManager.get_instance()

    try:
        # Primeiro verificar se existe e pegar docker_deployed atual
        resultado = await db.fetch_one(
            'SELECT docker_deployed FROM linodes WHERE id = ?',
            (linode['id'],)
        )
        docker_deployed = resultado[0] if resultado else 0

        # Manter o valor de docker_deployed se já existir
        await db.execute('''
            INSERT INTO linodes(id, label, status, ipv4, created, updated, docker_deployed, email_status)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ON CONFLICT(id) DO UPDATE SET
                label=excluded.label,
                status=excluded.status,
                ipv4=excluded.ipv4,
                created=excluded.created,
                updated=excluded.updated,
                docker_deployed=CASE
                    WHEN linodes.docker_deployed = 1 THEN 1
                    ELSE excluded.docker_deployed
                END,
                email_status=excluded.email_status
        ''', (
            linode['id'],
            linode['label'],
            linode['status'],
            json.dumps(linode['ipv4']),
            linode['created'],
            linode['updated'],
            docker_deployed,
            linode.get('email_status', 'Desconhecido')
        ))

    except Exception as e:
        print(f"Erro ao salvar/atualizar linode {linode['id']}: {e}")
        raise

def salvar_servidor_ativo(linode_id, label, ip, email_status):
    """Salva um servidor na tabela serversAtivos."""
    conn = sqlite3.connect(DB_PATH)
    c = conn.cursor()

    # Verificar se o servidor já existe na tabela
    c.execute('SELECT id FROM serversAtivos WHERE linode_id = ?', (linode_id,))
    resultado = c.fetchone()

    if resultado:
        print(f"Servidor {label} ({ip}) já está registrado como ativo.")
        conn.close()
        return resultado[0]

    # Gerar token e URL
    token = gerar_token()
    url = f"http://{ip}"

    # Inserir novo servidor ativo
    c.execute('''
        INSERT INTO serversAtivos(linode_id, label, ip, url, status, email_status, token)
        VALUES (?, ?, ?, ?, ?, ?, ?)
    ''', (linode_id, label, ip, url, 1, email_status, token))

    servidor_id = c.lastrowid
    conn.commit()
    conn.close()

    print(f"Servidor {label} ({ip}) salvo como ativo com ID {servidor_id}")
    return servidor_id

async def obter_servidores_ativos():
    """Retorna todos os servidores ativos."""
    db = DatabaseManager.get_instance()
    rows = await db.fetch_all(
        'SELECT id, linode_id, label, ip, url, status, email_status, token, created_at FROM serversAtivos WHERE status = 1'
    )

    return [{
        'id': row[0],
        'linode_id': row[1],
        'label': row[2],
        'ip': row[3],
        'url': row[4],
        'status': row[5],
        'email_status': row[6],
        'token': row[7],
        'created_at': row[8]
    } for row in rows]

def obter_lindoes_db():
    conn = sqlite3.connect(DB_PATH)
    c = conn.cursor()
    c.execute('SELECT id, label, status, ipv4, created, updated, docker_deployed, email_status FROM linodes')
    rows = c.fetchall()
    conn.close()
    linodes = []
    for row in rows:
        linodes.append({
            'id': row[0],
            'label': row[1],
            'status': row[2],
            'ipv4': json.loads(row[3]),
            'created': row[4],
            'updated': row[5],
            'docker_deployed': row[6],
            'email_status': row[7] if len(row) > 7 and row[7] else 'Desconhecido'
        })
    return linodes

# --- API Linode ---

def listar_lindoes_api():
    url = "https://api.linode.com/v4/linode/instances"
    try:
        response = requests.get(url, headers=headers, proxies=proxies, timeout=30)
        response.raise_for_status()
        data = response.json()
        return data.get('data', [])
    except Exception as e:
        print(f"Erro ao listar Linodes da API: {e}")
        return []

async def criar_maquina(label):
    url = "https://api.linode.com/v4/linode/instances"
    data = {
        "image": "linode/ubuntu22.04",
        "type": TIPO,
        "region": REGIAO,
        "root_pass": ROOT_PASS,
        "label": label
    }
    try:
        response = requests.post(url, json=data, headers=headers, proxies=proxies, timeout=30)
        response.raise_for_status()
        linode = response.json()
        await salvar_ou_atualizar_linode(linode)
        return linode
    except requests.exceptions.RequestException as e:
        print(f"Erro ao criar máquina {label}: {e}")
        return None

async def obter_status_linode(linode_id):
    url = f"https://api.linode.com/v4/linode/instances/{linode_id}"
    try:
        response = requests.get(url, headers=headers, proxies=proxies, timeout=30)
        response.raise_for_status()
        linode = response.json()
        await salvar_ou_atualizar_linode(linode)
        return linode['status'], linode
    except Exception as e:
        print(f"Erro ao obter status do Linode {linode_id}: {e}")
        return None, None

def deletar_linode(linode_id):
    url = f"https://api.linode.com/v4/linode/instances/{linode_id}"
    try:
        response = requests.delete(url, headers=headers, proxies=proxies, timeout=30)
        response.raise_for_status()
        print(f"Linode {linode_id} deletado com sucesso.")
        return True
    except Exception as e:
        print(f"Erro ao deletar Linode {linode_id}: {e}")
        return False

# --- Monitoramento ---

async def aguardar_linode_ativo(linode_id, timeout=600, intervalo=30):
    print(f"Aguardando Linode {linode_id} ficar ativo...")
    tempo_inicial = time.time()
    contador = 0
    while time.time() - tempo_inicial < timeout:
        status, linode = await obter_status_linode(linode_id)
        contador += 1
        tempo_decorrido = int(time.time() - tempo_inicial)

        if status == "running":
            print(f"Linode {linode_id} está ativo após {tempo_decorrido} segundos!")
            return linode

        # Mostrar progresso a cada verificação
        print(f"[{contador}] Status atual: {status}. Tempo decorrido: {tempo_decorrido}s. Aguardando mais {intervalo}s...")
        time.sleep(intervalo)

    print(f"Timeout de {timeout}s atingido aguardando Linode {linode_id} ficar ativo.")
    return None

# --- SSH Assíncrono ---

async def executar_comando_ssh(ip, comando, usuario="root", senha=ROOT_PASS):
    try:
        async with asyncssh.connect(
            ip,
            username=usuario,
            password=senha,
            known_hosts=None
        ) as conn:
            result = await conn.run(comando, check=False)
            return result.stdout.strip(), result.stderr.strip()
    except (asyncssh.Error, OSError) as e:
        return None, str(e)

async def verificar_docker_instalado(ip, usuario="root", senha=ROOT_PASS):
    comando = "docker --version 2>/dev/null || echo 'Docker não instalado'"
    stdout, _ = await executar_comando_ssh(ip, comando, usuario, senha)
    docker_instalado = stdout is not None and "Docker version" in stdout

    if docker_instalado:
        # Verificar se o serviço do Docker está rodando
        comando_service = "systemctl is-active docker 2>/dev/null || echo 'inactive'"
        stdout, _ = await executar_comando_ssh(ip, comando_service, usuario, senha)
        service_ativo = stdout is not None and stdout.strip() == "active"

        if not service_ativo:
            print(f"Docker está instalado em {ip}, mas o serviço não está ativo. Tentando iniciar...")
            comando_start = "systemctl start docker"
            await executar_comando_ssh(ip, comando_start, usuario, senha)

            # Verificar novamente
            stdout, _ = await executar_comando_ssh(ip, comando_service, usuario, senha)
            service_ativo = stdout is not None and stdout.strip() == "active"

            if service_ativo:
                print(f"Serviço Docker iniciado com sucesso em {ip}")
            else:
                print(f"Falha ao iniciar o serviço Docker em {ip}")
                return False

    return docker_instalado

async def copiar_arquivo_ssh(ip, arquivo_local, arquivo_remoto, usuario="root", senha=ROOT_PASS):
    try:
        async with asyncssh.connect(
            ip,
            username=usuario,
            password=senha,
            known_hosts=None
        ) as conn:
            await asyncssh.scp(arquivo_local, (conn, arquivo_remoto))
            return True, None
    except (asyncssh.Error, OSError) as e:
        return False, str(e)

async def copiar_diretorio_ssh(ip, diretorio_local, diretorio_remoto, usuario="root", senha=ROOT_PASS):
    try:
        # Primeiro, criar o diretório remoto principal
        comando_mkdir_principal = f"mkdir -p {diretorio_remoto}"
        stdout, stderr = await executar_comando_ssh(ip, comando_mkdir_principal, usuario, senha)
        if stderr and "Warning" not in stderr:
            print(f"Aviso ao criar diretório principal: {stderr}")

        # Listar todos os arquivos a serem copiados
        arquivos_para_copiar = []
        for root, _, files in os.walk(diretorio_local):
            for file in files:
                arquivo_local = os.path.join(root, file)
                arquivo_remoto = os.path.join(diretorio_remoto, os.path.relpath(arquivo_local, diretorio_local))
                arquivos_para_copiar.append((arquivo_local, arquivo_remoto))

        print(f"Total de {len(arquivos_para_copiar)} arquivos para copiar")

        # Copiar cada arquivo
        async with asyncssh.connect(
            ip,
            username=usuario,
            password=senha,
            known_hosts=None
        ) as conn:
            for arquivo_local, arquivo_remoto in arquivos_para_copiar:
                # Criar diretórios remotos necessários
                dir_remoto = os.path.dirname(arquivo_remoto)
                comando_mkdir = f"mkdir -p {dir_remoto}"
                stdout, stderr = await executar_comando_ssh(ip, comando_mkdir, usuario, senha)
                if stderr and "Warning" not in stderr:
                    print(f"Aviso ao criar diretório remoto {dir_remoto}: {stderr}")

                # Copiar arquivo
                try:
                    print(f"Copiando {arquivo_local} para {arquivo_remoto}")
                    await asyncssh.scp(arquivo_local, (conn, arquivo_remoto))
                except (asyncssh.Error, OSError) as e:
                    print(f"Erro ao copiar arquivo {arquivo_local}: {e}")
                    return False, str(e)

            # Verificar se todos os arquivos foram copiados
            for arquivo_local, arquivo_remoto in arquivos_para_copiar:
                comando_verificar = f"test -f {arquivo_remoto} && echo 'OK' || echo 'FALHA'"
                stdout, stderr = await executar_comando_ssh(ip, comando_verificar, usuario, senha)
                if stdout.strip() != "OK":
                    print(f"Arquivo {arquivo_remoto} não foi copiado corretamente")
                    return False, f"Arquivo {arquivo_remoto} não foi copiado corretamente"

            return True, None
    except (asyncssh.Error, OSError) as e:
        return False, str(e)

async def instalar_docker(ip, usuario="root", senha=ROOT_PASS):
    # Verificar se o Docker já está instalado
    docker_instalado = await verificar_docker_instalado(ip, usuario, senha)
    if docker_instalado:
        print(f"Docker já está instalado em {ip}")
        return True

    print(f"Instalando Docker em {ip}...")

    # Comandos para instalar o Docker usando o script oficial da Docker
    comandos = [
        "apt-get update",
        "apt-get install -y curl",
        "curl -fsSL https://get.docker.com -o get-docker.sh",
        "sh get-docker.sh",
        "rm get-docker.sh",
        "systemctl enable docker",
        "systemctl start docker"
    ]

    for comando in comandos:
        print(f"Executando: {comando}")
        stdout, stderr = await executar_comando_ssh(ip, comando, usuario, senha)
        if stderr and "Warning" not in stderr:  # Ignorar avisos
            print(f"Erro ao executar '{comando}': {stderr}")
        if stdout:
            print(f"Saída: {stdout[:100]}..." if len(stdout) > 100 else f"Saída: {stdout}")
            # Continuar mesmo com erro, pois alguns comandos podem falhar mas a instalação continuar

    # Verificar se o Docker foi instalado com sucesso
    docker_instalado = await verificar_docker_instalado(ip, usuario, senha)
    if docker_instalado:
        print(f"Docker instalado com sucesso em {ip}")

        # Instalar Docker Compose
        print("Instalando Docker Compose...")
        compose_comandos = [
            "curl -L \"https://github.com/docker/compose/releases/download/v2.24.5/docker-compose-$(uname -s)-$(uname -m)\" -o /usr/local/bin/docker-compose",
            "chmod +x /usr/local/bin/docker-compose"
        ]

        for comando in compose_comandos:
            stdout, stderr = await executar_comando_ssh(ip, comando, usuario, senha)
            if stderr and "Warning" not in stderr:
                print(f"Aviso ao instalar Docker Compose: {stderr}")
            if stdout:
                print(f"Saída: {stdout[:100]}..." if len(stdout) > 100 else f"Saída: {stdout}")

        return True
    else:
        print(f"Falha ao instalar Docker em {ip}")
        return False

async def deploy_docker(linode, usuario="root", senha=ROOT_PASS):
    ip = linode['ipv4'][0]
    deploy_dir_local = os.path.join(os.getcwd(), DEPLOY_DIR)
    deploy_dir_remoto = "/root/enviador"

    # Instalar Docker
    print(f"Instalando Docker em {ip}...")
    success = await instalar_docker(ip, usuario, senha)
    if not success:
        print(f"Falha ao instalar Docker em {ip}")
        return False

    # Criar diretório remoto
    comando_mkdir = f"mkdir -p {deploy_dir_remoto}"
    print(f"Criando diretório remoto {deploy_dir_remoto}...")
    stdout, stderr = await executar_comando_ssh(ip, comando_mkdir, usuario, senha)
    if stderr and "Warning" not in stderr:
        print(f"Aviso ao criar diretório: {stderr}")

    # Copiar diretório
    print(f"Copiando diretório {deploy_dir_local} para {ip}:{deploy_dir_remoto}")
    success, error = await copiar_diretorio_ssh(ip, deploy_dir_local, deploy_dir_remoto, usuario, senha)
    if not success:
        print(f"Erro ao copiar diretório: {error}")
        return False

    # Listar arquivos no diretório remoto para verificar
    comando_ls = f"ls -la {deploy_dir_remoto}"
    print(f"Verificando arquivos copiados para {deploy_dir_remoto}...")
    stdout, stderr = await executar_comando_ssh(ip, comando_ls, usuario, senha)
    print(f"Arquivos no diretório remoto:\n{stdout}")

    # Dar permissão de execução ao script de deploy
    comando_chmod = f"chmod +x {deploy_dir_remoto}/deploy.sh"
    print(f"Dando permissão de execução ao script de deploy...")
    stdout, stderr = await executar_comando_ssh(ip, comando_chmod, usuario, senha)
    if stderr and "Warning" not in stderr:
        print(f"Aviso ao dar permissão ao script: {stderr}")

    # Navegar para o diretório e executar o script de deploy COM O IP COMO PARÂMETRO
    comando_deploy = f"cd {deploy_dir_remoto} && bash ./deploy.sh {ip}"
    print(f"Executando deploy em {ip} com hostname baseado no IP...")
    stdout, stderr = await executar_comando_ssh(ip, comando_deploy, usuario, senha)
    if stderr and "Warning" not in stderr:
        print(f"Aviso ao executar deploy.sh: {stderr}")
    print(f"Saída do deploy: {stdout[:200]}..." if len(stdout) > 200 else f"Saída do deploy: {stdout}")

    # Verificar se o deploy foi bem-sucedido verificando se o container está rodando
    comando_verificar = "docker ps | grep api-enviador"
    stdout, stderr = await executar_comando_ssh(ip, comando_verificar, usuario, senha)
    if stdout and "api-enviador" in stdout:
        print(f"Container api-enviador está rodando em {ip}")
        return True
    else:
        print(f"Container api-enviador não está rodando em {ip}. Verificando logs...")
        comando_logs = "docker logs api-enviador 2>&1 | tail -n 20"
        stdout, stderr = await executar_comando_ssh(ip, comando_logs, usuario, senha)
        print(f"Logs do container: {stdout}")

        # Verificar se há problemas com o docker-compose
        comando_compose_logs = f"cd {deploy_dir_remoto} && docker-compose logs"
        stdout, stderr = await executar_comando_ssh(ip, comando_compose_logs, usuario, senha)
        print(f"Logs do docker-compose:\n{stdout}")

        return False
# --- Funções de Conexão SSH e Deploy Múltiplo ---

async def conectar_ssh(ip, usuario="root", senha=ROOT_PASS):
    print(f"Tentando conectar via SSH em {ip}...")
    try:
        async with asyncssh.connect(
            ip,
            username=usuario,
            password=senha,
            known_hosts=None
        ) as conn:
            print(f"Conectado via SSH em {ip}!")
            return True
    except (asyncssh.Error, OSError) as e:
        print(f"Erro na conexão SSH em {ip}: {e}")
        return False

async def conectar_multiplos_ssh(linodes):
    tasks = []
    for linode in linodes:
        if linode['status'] == 'running' and linode['ipv4']:
            ip = linode['ipv4'][0]
            tasks.append(conectar_ssh(ip))
    resultados = await asyncio.gather(*tasks)
    return all(resultados)  # Retorna True se todas as conexões forem bem-sucedidas

async def processar_falha_vm(linode_id, label, ip, email_status):
    """Processa a falha de uma VM com delay randômico antes da deleção"""
    delay = random.randint(30, 180)  # Delay entre 30 segundos e 3 minutos
    print(f"VM {label} ({ip}) falhou com status: {email_status}")
    print(f"Aguardando {delay} segundos antes de iniciar deleção...")

    await asyncio.sleep(delay)

    print(f"Iniciando processo de deleção para {label} ({ip})...")
    if deletar_linode(linode_id):
        conn = sqlite3.connect(DB_PATH)
        c = conn.cursor()
        c.execute("DELETE FROM linodes WHERE id = ?", (linode_id,))
        conn.commit()
        conn.close()
        print(f"✓ VM {label} ({ip}) deletada com sucesso")
        return True
    else:
        print(f"✗ Falha ao deletar VM {label} ({ip})")
        return False

async def deletar_todas_linodes_com_delay(delay=5):
    """Deleta todas as Linodes com um delay específico entre cada deleção.

    Args:
        delay (int): Tempo de espera em segundos entre cada deleção

    Returns:
        tuple: (total_vms, deletadas, falhas)
    """
    print(f"\n=== Iniciando deleção de todas as Linodes com delay de {delay}s ===")

    # Obter todas as VMs da API Linode
    linodes_api = listar_lindoes_api()

    if not linodes_api:
        print("Nenhuma Linode encontrada para deletar.")
        return 0, 0, 0

    total_vms = len(linodes_api)
    print(f"Encontradas {total_vms} Linodes para deletar.")

    # Confirmar a operação
    confirmacao = input(f"ATENÇÃO: Esta operação irá deletar TODAS as {total_vms} Linodes. Confirma? (s/n): ").lower()
    if confirmacao != 's':
        print("Operação cancelada pelo usuário.")
        return total_vms, 0, 0

    # Iniciar processo de deleção
    deletadas = 0
    falhas = 0

    for i, linode in enumerate(linodes_api, 1):
        linode_id = linode['id']
        label = linode['label']
        ip = linode['ipv4'][0] if linode['ipv4'] else "N/A"

        print(f"\n[{i}/{total_vms}] Deletando Linode {label} (ID: {linode_id}, IP: {ip})...")

        if deletar_linode(linode_id):
            # Remover do banco de dados local
            conn = sqlite3.connect(DB_PATH)
            c = conn.cursor()
            c.execute("DELETE FROM linodes WHERE id = ?", (linode_id,))

            # Também remover da tabela serversAtivos se existir
            c.execute("DELETE FROM serversAtivos WHERE linode_id = ?", (linode_id,))

            conn.commit()
            conn.close()

            print(f"✓ Linode {label} (ID: {linode_id}) deletada com sucesso")
            deletadas += 1
        else:
            print(f"✗ Falha ao deletar Linode {label} (ID: {linode_id})")
            falhas += 1

        # Aplicar delay entre as deleções (exceto para a última)
        if i < total_vms:
            print(f"Aguardando {delay} segundos antes da próxima deleção...")
            await asyncio.sleep(delay)

    # Resumo final
    print(f"\n=== Resumo da operação de deleção ===")
    print(f"Total de Linodes: {total_vms}")
    print(f"Deletadas com sucesso: {deletadas}")
    print(f"Falhas na deleção: {falhas}")

    return total_vms, deletadas, falhas

async def verificar_status_api(ip, max_tentativas=8):
    """Verifica o status da API com múltiplas tentativas, processa falhas com delay"""
    for tentativa in range(max_tentativas):
        try:
            url = f"http://{ip}/postfix-status"
            print(f"Tentativa {tentativa + 1} de {max_tentativas} para {ip}...")

            response = requests.get(url, timeout=10)
            if response.status_code == 200:
                data = response.json()
                email_status = data.get('emailStatus', 'Desconhecido')
                print(f"Status do email em {ip}: {email_status}")

                conn = sqlite3.connect(DB_PATH)
                c = conn.cursor()
                c.execute('SELECT id, label FROM linodes WHERE ipv4 LIKE ?', (f'%"{ip}"%',))
                linode_info = c.fetchone()

                if linode_info:
                    linode_id, label = linode_info
                    c.execute('UPDATE linodes SET email_status = ? WHERE id = ?',
                             (email_status, linode_id))
                    conn.commit()
                    conn.close()

                    if email_status == "Entregue":
                        salvar_servidor_ativo(linode_id, label, ip, email_status)
                        print(f"✓ Servidor {label} ({ip}) salvo como ativo com status 'Entregue'")
                        return True, email_status
                    elif "Falha" in email_status:
                        await processar_falha_vm(linode_id, label, ip, email_status)
                        return False, email_status

            if tentativa < max_tentativas - 1:
                delay = random.randint(5, 15)
                print(f"Aguardando {delay} segundos antes da próxima tentativa...")
                await asyncio.sleep(delay)

        except Exception as e:
            print(f"Erro na tentativa {tentativa + 1}: {str(e)}")
            if tentativa < max_tentativas - 1:
                await asyncio.sleep(5)

    # Se chegou aqui, todas as tentativas falharam
    conn = sqlite3.connect(DB_PATH)
    c = conn.cursor()
    c.execute('SELECT id, label FROM linodes WHERE ipv4 LIKE ?', (f'%"{ip}"%',))
    linode_info = c.fetchone()
    conn.close()

    if linode_info:
        linode_id, label = linode_info
        await processar_falha_vm(linode_id, label, ip, "Falha após máximo de tentativas")

    return False, "Falha após máximo de tentativas"

async def verificar_status_api_com_retry(ip, max_tentativas=8):
    """Verifica o status da API com múltiplas tentativas"""
    for tentativa in range(max_tentativas):
        try:
            print(f"Tentativa {tentativa + 1} de {max_tentativas} para {ip}...")
            status_ok, email_status = await verificar_status_api(ip)

            if status_ok and email_status == "Entregue":
                return True, email_status

            if tentativa < max_tentativas - 1:
                delay = random.randint(5, 15)
                print(f"Aguardando {delay} segundos antes da próxima tentativa...")
                await asyncio.sleep(delay)

        except Exception as e:
            print(f"Erro na tentativa {tentativa + 1}: {str(e)}")

            if tentativa < max_tentativas - 1:
                await asyncio.sleep(5)

    # Se chegou aqui, todas as tentativas falharam
    conn = sqlite3.connect(DB_PATH)
    c = conn.cursor()
    c.execute('SELECT id, label FROM linodes WHERE ipv4 LIKE ?', (f'%"{ip}"%',))
    linode_info = c.fetchone()

    if linode_info:
        linode_id, label = linode_info
        await processar_falha_vm(linode_id, label, ip, "Falha após máximo de tentativas")

    return False, "Falha após máximo de tentativas"

async def deployar_docker_multiplas(linodes):
    print("\nIniciando deploy do Docker em múltiplas máquinas...")

    # Criar tarefas para configurar cada máquina
    tasks = []
    linodes_para_deploy = []
    for linode in linodes:
        if linode['status'] == 'running' and linode['ipv4']:
            tasks.append(configurar_maquina(linode))
            linodes_para_deploy.append(linode)

    # Executar todas as tarefas em paralelo
    print(f"Iniciando configuração em {len(tasks)} máquinas simultaneamente...")
    resultados = await asyncio.gather(*tasks)

    # Processar resultados
    await processar_resultados_configuracao(resultados)

    # Contar sucessos para retornar
    sucessos = sum(1 for sucesso, _ in resultados if sucesso)
    return sucessos > 0

# --- Sincronização inicial ---

async def sincronizar_banco_com_api():
    print("Sincronizando banco local com Linode API...")

    db = DatabaseManager.get_instance()

    # Obter VMs da API Linode
    linodes_api = listar_lindoes_api()
    ids_api = set(vm['id'] for vm in linodes_api)

    try:
        # Contar total de registros antes da limpeza
        total_antes = await db.fetch_one('SELECT COUNT(*) FROM linodes')
        total_antes = total_antes[0]

        # Deletar todas as VMs que não existem na API
        if ids_api:
            await db.execute(
                'DELETE FROM linodes WHERE id NOT IN (%s)' % ','.join(map(str, ids_api))
            )
        else:
            await db.execute('DELETE FROM linodes')

        registros_removidos = total_antes - len(ids_api)
        if registros_removidos > 0:
            print(f"Removidos {registros_removidos} registros obsoletos do banco")

        # Atualizar/inserir VMs atuais da API
        for linode in linodes_api:
            await salvar_ou_atualizar_linode(linode)

        print(f"Sincronização concluída. Mantidas {len(linodes_api)} máquinas ativas no banco.")

    except Exception as e:
        print(f"Erro durante a sincronização: {e}")
        raise

# --- Funções de Configuração ---

async def configurar_maquina(linode):
    """Configura uma única máquina: instala Docker e faz deploy."""
    if not linode or linode['status'] != 'running' or not linode['ipv4']:
        print(f"Máquina {linode.get('label', 'desconhecida')} não está pronta para configuração.")
        return False, None

    ip = linode['ipv4'][0]
    print(f"\nIniciando configuração da máquina {linode['label']} ({ip})...")

    # Tentar conectar via SSH primeiro
    print(f"Testando conexão SSH com {ip}...")
    ssh_conectado = await conectar_ssh(ip)
    if not ssh_conectado:
        print(f"Não foi possível conectar via SSH em {ip}. Pulando configuração.")
        return False, None

    # Fazer deploy do Docker
    print(f"Iniciando deploy do Docker em {ip}...")
    sucesso = await deploy_docker(linode, "root", ROOT_PASS)

    email_status = None
    if sucesso:
        print(f"Deploy concluído com sucesso em {linode['label']} ({ip})")

        # Verificar status da API
        print(f"Verificando status da API em {ip}...")
        status_ok, email_status = await verificar_status_api(ip)

        if status_ok:
            print(f"Status da API em {ip}: {email_status}")
            # A função verificar_status_api já processa o resultado (salva ou deleta)

            # Adicionar informações extras para servidores entregues
            if email_status == "Entregue":
                print(f"\n=== SERVIDOR ENTREGUE COM SUCESSO ===")
                print(f"Label: {linode['label']}")
                print(f"IP: {ip}")
                print(f"URL: http://{ip}")
                print(f"Status: {email_status}")

                # Obter o token do servidor ativo
                conn = sqlite3.connect(DB_PATH)
                c = conn.cursor()
                c.execute('SELECT token FROM serversAtivos WHERE linode_id = ?', (linode['id'],))
                resultado = c.fetchone()
                conn.close()

                if resultado:
                    token = resultado[0]
                    print(f"Token: {token}")
                    print("=== SERVIDOR PRONTO PARA USO ===")
        else:
            print(f"Status da API em {ip}: {email_status}")
            print(f"Não foi possível verificar o status da API em {ip}")
    else:
        print(f"Falha no deploy em {linode['label']} ({ip})")

    return sucesso, email_status

async def processar_resultados_configuracao(resultados):
    """Processa os resultados da configuração de múltiplas máquinas."""
    sucessos = 0
    falhas = 0
    entregues = 0
    falha_email = 0

    for sucesso, status in resultados:
        if sucesso:
            sucessos += 1
            if status == "Entregue":
                entregues += 1
            elif status and "Falha" in status:
                falha_email += 1
        else:
            falhas += 1

    print(f"\n=== Resumo da Configuração ===")
    print(f"Total de máquinas processadas: {len(resultados)}")
    print(f"Deploy bem-sucedido: {sucessos}")
    print(f"Deploy falhou: {falhas}")
    print(f"Servidores entregues: {entregues}")
    print(f"Servidores com falha de email: {falha_email}")

    if entregues > 0:
        listar = input("\nDeseja listar os servidores ativos? (s/n): ").lower()
        if listar == 's':
            await listar_servidores_ativos_menu()

async def limpar_banco_dados():
    """Remove VMs deletadas e mantém apenas as entregues em servers ativos"""
    print("\nIniciando limpeza do banco de dados...")

    # Obter VMs da API Linode
    vms_api = listar_lindoes_api()
    ids_api = set(vm['id'] for vm in vms_api)

    # Obter VMs do banco local
    conn = sqlite3.connect(DB_PATH)
    c = conn.cursor()

    # Obter IDs de servidores entregues
    c.execute('SELECT linode_id FROM serversAtivos WHERE email_status = "Entregue"')
    ids_entregues = set(row[0] for row in c.fetchall())

    # Obter todas as VMs do banco
    c.execute('SELECT id FROM linodes')
    ids_banco = set(row[0] for row in c.fetchall())

    # Identificar VMs para deletar (não existem na API e não estão entregues)
    ids_para_deletar = ids_banco - (ids_api | ids_entregues)

    # Deletar VMs obsoletas
    if ids_para_deletar:
        c.execute('DELETE FROM linodes WHERE id IN (%s)' % ','.join(map(str, ids_para_deletar)))
        print(f"Removidas {len(ids_para_deletar)} VMs obsoletas do banco")

    conn.commit()
    conn.close()
    return len(ids_para_deletar)

async def configurar_vms_pendentes():
    """Configura todas as VMs que ainda não foram configuradas ou tiveram erro"""
    print("\nBuscando VMs pendentes de configuração...")

    conn = sqlite3.connect(DB_PATH)
    c = conn.cursor()

    # Obter VMs que não estão em serversAtivos e não têm docker_deployed
    c.execute('''
        SELECT l.* FROM linodes l
        LEFT JOIN serversAtivos s ON l.id = s.linode_id
        WHERE s.id IS NULL
        AND l.docker_deployed = 0
        AND l.status = 'running'
    ''')

    vms_pendentes = []
    for row in c.fetchall():
        vms_pendentes.append({
            'id': row[0],
            'label': row[1],
            'status': row[2],
            'ipv4': json.loads(row[3]),
            'created': row[4],
            'updated': row[5],
            'docker_deployed': row[6],
            'email_status': row[7]
        })

    conn.close()

    if not vms_pendentes:
        print("Não há VMs pendentes de configuração")
        return 0

    print(f"Encontradas {len(vms_pendentes)} VMs pendentes. Iniciando configuração...")
    return await deployar_docker_multiplas(vms_pendentes)

def deletar_vms_com_erro():
    """Deleta VMs que tiveram erro na configuração"""
    print("\nBuscando VMs com erro...")

    conn = sqlite3.connect(DB_PATH)
    c = conn.cursor()

    # Buscar VMs com status de erro
    c.execute("SELECT id, label FROM linodes WHERE email_status LIKE '%Falha%'")
    vms_com_erro = c.fetchall()

    if not vms_com_erro:
        print("Não há VMs com erro para deletar")
        return 0

    print(f"Encontradas {len(vms_com_erro)} VMs com erro. Iniciando deleção...")

    deletadas = 0
    for vm_id, label in vms_com_erro:
        print(f"Deletando VM {label} (ID: {vm_id})...")
        if deletar_linode(vm_id):
            c.execute("DELETE FROM linodes WHERE id = ?", (vm_id,))
            deletadas += 1

    conn.commit()
    conn.close()
    return deletadas

# --- Funções do Menu ---

async def verificar_instalacao_docker_menu():
    linodes = obter_lindoes_db()
    if not linodes:
        print("Nenhuma máquina encontrada no banco de dados.")
        return

    print("\nMáquinas disponíveis:")
    for i, linode in enumerate(linodes, 1):
        print(f"{i}. {linode['label']} (ID: {linode['id']}) - Status: {linode['status']}")

    try:
        escolha = int(input("\nEscolha o número da máquina para verificar (0 para cancelar): "))
        if escolha == 0:
            return
        if escolha < 1 or escolha > len(linodes):
            print("Número inválido!")
            return

        linode = linodes[escolha - 1]
        if linode['status'] != 'running':
            print(f"Máquina não está ativa (status: {linode['status']})")
            return

        if not linode['ipv4']:
            print("Máquina não possui IP válido")
            return

        ip = linode['ipv4'][0]
        docker_instalado = await verificar_docker_instalado(ip)

        if docker_instalado:
            print(f"Docker está instalado em {linode['label']}")
        else:
            print(f"Docker NÃO está instalado em {linode['label']}")

    except ValueError:
        print("Entrada inválida!")

async def deployar_docker_menu():
    linodes = obter_lindoes_db()
    if not linodes:
        print("Nenhuma máquina encontrada no banco de dados.")
        return

    print("\nMáquinas disponíveis:")
    for i, linode in enumerate(linodes, 1):
        print(f"{i}. {linode['label']} (ID: {linode['id']}) - Status: {linode['status']}")

    try:
        escolha = int(input("\nEscolha o número da máquina para deployar (0 para cancelar): "))
        if escolha == 0:
            return
        if escolha < 1 or escolha > len(linodes):
            print("Número inválido!")
            return

        linode = linodes[escolha - 1]
        if linode['status'] != 'running':
            print(f"Máquina não está ativa (status: {linode['status']})")
            return

        if not linode['ipv4']:
            print("Máquina não possui IP válido")
            return

        # Deploy Docker
        sucesso = await deploy_docker(linode, "root", ROOT_PASS)

        if sucesso:
            print(f"Docker implantado com sucesso em {linode['label']}")
            # Atualizar o banco de dados
            linode['docker_deployed'] = 1
            await salvar_ou_atualizar_linode(linode)
        else:
            print(f"Falha ao implantar Docker em {linode['label']}")

    except ValueError:
        print("Entrada inválida!")

async def conectar_maquina_existente():
    linodes = obter_lindoes_db()
    if not linodes:
        print("Nenhuma máquina encontrada no banco de dados.")
        return

    print("\nMáquinas disponíveis:")
    for i, linode in enumerate(linodes, 1):
        print(f"{i}. {linode['label']} (ID: {linode['id']}) - Status: {linode['status']}")

    try:
        escolha = int(input("\nEscolha o número da máquina para conectar (0 para cancelar): "))
        if escolha == 0:
            return
        if escolha < 1 or escolha > len(linodes):
            print("Número inválido!")
            return

        linode = linodes[escolha - 1]
        if linode['status'] != 'running':
            print(f"Máquina não está ativa (status: {linode['status']})")
            return

        if not linode['ipv4']:
            print("Máquina não possui IP válido")
            return

        # Conecta via SSH
        ip = linode['ipv4'][0]
        await conectar_ssh(ip)

    except ValueError:
        print("Entrada inválida!")

async def criar_novas_maquinas():
    quantidade = int(input("Quantas máquinas deseja criar? "))
    configuracao_automatica = input("Deseja configurar automaticamente as máquinas assim que estiverem prontas? (s/n): ").lower() == 's'

    print(f"\nCriando {quantidade} máquinas...")
    novas_maquinas = []
    for i in range(quantidade):
        # Aplicar delay aleatório entre 10 segundos e 1 minuto antes de criar cada máquina
        if i > 0:  # Não aplicar delay para a primeira máquina
            delay = random.randint(10, 60)
            print(f"Aguardando {delay} segundos antes de criar a próxima máquina...")
            time.sleep(delay)

        id_unico = str(uuid.uuid4())[:8]
        label = f"ubuntu-{id_unico}-{i+1}"
        print(f"Criando máquina {label}...")
        linode = await criar_maquina(label)
        if linode:
            novas_maquinas.append(linode)
            print(f"Máquina {label} (ID: {linode['id']}) criada com sucesso. Aguardando ficar ativa...")
        else:
            print(f"Falha ao criar máquina {label}")

    if not novas_maquinas:
        print("Nenhuma máquina foi criada com sucesso.")
        return

    # Monitorar e configurar cada máquina individualmente
    linodes_ativos = []
    resultados = []

    for linode in novas_maquinas:
        # Aguardar a máquina ficar ativa
        linode_atual = await aguardar_linode_ativo(linode['id'])
        if not linode_atual:
            print(f"Máquina {linode['label']} (ID: {linode['id']}) não ficou ativa no tempo esperado.")
            continue

        linodes_ativos.append(linode_atual)
        print(f"Máquina {linode_atual['label']} (ID: {linode_atual['id']}) está ativa!")

        # Se a configuração automática estiver ativada, configurar imediatamente
        if configuracao_automatica:
            print(f"Iniciando configuração da máquina {linode_atual['label']}...")
            resultado = await configurar_maquina(linode_atual)
            resultados.append(resultado)

    # Processar resultados da configuração se houver
    if configuracao_automatica and resultados:
        print("\nProcessando resultados da configuração...")
        await processar_resultados_configuracao(resultados)

    # Resumo final
    print(f"\n=== Resumo da criação de máquinas ===")
    print(f"Total de máquinas solicitadas: {quantidade}")
    print(f"Máquinas criadas com sucesso: {len(novas_maquinas)}")
    print(f"Máquinas ativas: {len(linodes_ativos)}")
    if configuracao_automatica:
        print(f"Máquinas configuradas: {len(resultados)}")

    # Se não foi feita configuração automática, perguntar se deseja fazer deploy em todas
    if linodes_ativos and not configuracao_automatica:
        resposta = input("\nDeseja fazer deploy do Docker em todas as máquinas agora? (s/n): ").lower()
        if resposta == 's':
            await deployar_docker_multiplas(linodes_ativos)
    elif not linodes_ativos:
        print("Nenhuma máquina ativa para configurar.")

async def verificar_status_api_menu():
    linodes = obter_lindoes_db()
    if not linodes:
        print("Nenhuma máquina encontrada no banco de dados.")
        return

    print("\nMáquinas disponíveis:")
    for i, linode in enumerate(linodes, 1):
        print(f"{i}. {linode['label']} (ID: {linode['id']}) - Status: {linode['status']} - Email: {linode.get('email_status', 'Desconhecido')}")

    try:
        escolha = input("\nEscolha o número da máquina para verificar status da API (0 para todas, -1 para cancelar): ")
        if escolha == "-1":
            return

        if escolha == "0":
            # Verificar todas as máquinas
            print("\nVerificando status da API em todas as máquinas...")
            tasks = []
            for linode in linodes:
                if linode['status'] == 'running' and linode['ipv4']:
                    ip = linode['ipv4'][0]
                    tasks.append(verificar_status_api(ip))

            if tasks:
                await asyncio.gather(*tasks)
            else:
                print("Nenhuma máquina ativa para verificar.")
            return

        escolha = int(escolha)
        if escolha < 1 or escolha > len(linodes):
            print("Número inválido!")
            return

        linode = linodes[escolha - 1]
        if linode['status'] != 'running':
            print(f"Máquina não está ativa (status: {linode['status']})")
            return

        if not linode['ipv4']:
            print("Máquina não possui IP válido")
            return

        ip = linode['ipv4'][0]
        await verificar_status_api(ip)

    except ValueError:
        print("Entrada inválida!")

async def listar_servidores_ativos_menu():
    servidores = await obter_servidores_ativos()
    if not servidores:
        print("Nenhum servidor ativo encontrado.")
        return

    print("\n=== Servidores Ativos ===")
    print("ID | Label | IP | URL | Status | Token")
    print("-" * 80)

    for servidor in servidores:
        print(f"{servidor['id']} | {servidor['label']} | {servidor['ip']} | {servidor['url']} | {servidor['email_status']} | {servidor['token']}")

    print(f"\nTotal de servidores ativos: {len(servidores)}")

    # Opção para exportar para CSV
    exportar = input("\nDeseja exportar a lista para um arquivo CSV? (s/n): ").lower()
    if exportar == 's':
        try:
            import csv
            arquivo = "servidores_ativos.csv"
            with open(arquivo, 'w', newline='') as f:
                writer = csv.writer(f)
                writer.writerow(['ID', 'Label', 'IP', 'URL', 'Status', 'Token', 'Data de Criação'])
                for servidor in servidores:
                    writer.writerow([
                        servidor['id'],
                        servidor['label'],
                        servidor['ip'],
                        servidor['url'],
                        servidor['email_status'],
                        servidor['token'],
                        servidor['created_at']
                    ])
            print(f"Lista exportada para {arquivo}")
        except Exception as e:
            print(f"Erro ao exportar para CSV: {e}")

async def verificar_docker_todas_maquinas():
    """Verifica instalação do Docker em todas as máquinas ativas de forma assíncrona."""
    print("\nVerificando instalação do Docker em todas as máquinas...")
    linodes = obter_lindoes_db()
    db = DatabaseManager.get_instance()

    async def verificar_maquina(linode):
        if linode['status'] != 'running' or not linode['ipv4']:
            return None

        ip = linode['ipv4'][0]
        print(f"Verificando Docker em {linode['label']} ({ip})...")
        docker_instalado = await verificar_docker_instalado(ip)

        if docker_instalado:
            await db.execute(
                'UPDATE linodes SET docker_deployed = 1 WHERE id = ?',
                (linode['id'],)
            )
            print(f"✓ Docker confirmado em {linode['label']} - Banco atualizado")
        else:
            print(f"✗ Docker não encontrado em {linode['label']}")

        return {
            'linode': linode,
            'instalado': docker_instalado
        }

    # Criar tarefas para todas as máquinas
    tarefas = [verificar_maquina(linode) for linode in linodes]
    resultados = await asyncio.gather(*tarefas)

    # Filtrar resultados None e contar
    resultados = [r for r in resultados if r is not None]
    total = len(resultados)
    instalados = sum(1 for r in resultados if r['instalado'])

    print(f"\nResultado: Docker instalado em {instalados} de {total} máquinas.")
    return resultados

async def deployar_docker_todas_maquinas():
    """Deploy do Docker em múltiplas máquinas de forma assíncrona."""
    print("\nIniciando deploy do Docker em máquinas pendentes...")
    linodes = obter_lindoes_db()
    db = DatabaseManager.get_instance()

    async def deployar_maquina(linode):
        if linode['status'] != 'running' or not linode['ipv4']:
            return None

        # Verificar se já está configurada
        resultado = await db.fetch_one(
            'SELECT docker_deployed FROM linodes WHERE id = ? AND docker_deployed = 1',
            (linode['id'],)
        )

        if resultado:
            print(f"Pulando {linode['label']}: Docker já configurado")
            return {
                'linode': linode,
                'status': 'já configurado',
                'sucesso': True
            }

        ip = linode['ipv4'][0]
        print(f"\nIniciando deploy em {linode['label']} ({ip})...")

        # Tentar instalar o Docker
        sucesso = await instalar_docker(ip)

        if sucesso:
            # Verificar se a instalação foi bem-sucedida
            docker_funcionando = await verificar_docker_instalado(ip)
            if docker_funcionando:
                await db.execute(
                    'UPDATE linodes SET docker_deployed = 1 WHERE id = ?',
                    (linode['id'],)
                )
                print(f"✓ Deploy concluído com sucesso em {linode['label']}")
                return {
                    'linode': linode,
                    'status': 'sucesso',
                    'sucesso': True
                }
            else:
                print(f"✗ Falha na verificação pós-instalação em {linode['label']}")
                return {
                    'linode': linode,
                    'status': 'falha na verificação',
                    'sucesso': False
                }
        else:
            print(f"✗ Falha no deploy em {linode['label']}")
            return {
                'linode': linode,
                'status': 'falha no deploy',
                'sucesso': False
            }

    # Criar tarefas para todas as máquinas
    tarefas = [deployar_maquina(linode) for linode in linodes]
    resultados = await asyncio.gather(*tarefas)

    # Filtrar resultados None e contar
    resultados = [r for r in resultados if r is not None]
    total = len(resultados)
    sucessos = sum(1 for r in resultados if r['sucesso'])

    print(f"\nDeploy concluído: {sucessos} de {total} máquinas configuradas com sucesso.")
    return resultados

async def verificar_status_postfix_todas_maquinas():
    """Verifica status do Postfix apenas em máquinas com deploy concluído."""
    print("\nVerificando status do Postfix em máquinas configuradas...")

    # Obter apenas máquinas com deploy concluído
    conn = sqlite3.connect(DB_PATH)
    c = conn.cursor()
    c.execute('''
        SELECT id, label, ipv4, status, docker_deployed
        FROM linodes
        WHERE docker_deployed = 1
        AND status = 'running'
    ''')

    linodes = []
    for row in c.fetchall():
        linodes.append({
            'id': row[0],
            'label': row[1],
            'ipv4': json.loads(row[2]),
            'status': row[3],
            'docker_deployed': row[4]
        })
    conn.close()

    if not linodes:
        print("Nenhuma máquina configurada encontrada.")
        return []

    print(f"Encontradas {len(linodes)} máquinas configuradas.")

    async def verificar_maquina(linode):
        if not linode['ipv4']:
            return None

        ip = linode['ipv4'][0]
        print(f"\nVerificando Postfix em {linode['label']} ({ip})...")

        status_ok, mensagem = await verificar_status_api(ip)

        # Atualizar status no banco
        conn = sqlite3.connect(DB_PATH)
        c = conn.cursor()
        c.execute(
            'UPDATE linodes SET email_status = ? WHERE id = ?',
            (mensagem, linode['id'])
        )
        conn.commit()
        conn.close()

        if status_ok:
            print(f"✓ {linode['label']}: {mensagem}")
        else:
            print(f"✗ {linode['label']}: {mensagem}")

        return {
            'linode': linode,
            'status_ok': status_ok,
            'mensagem': mensagem
        }

    # Criar tarefas para todas as máquinas
    tarefas = [verificar_maquina(linode) for linode in linodes]
    resultados = await asyncio.gather(*tarefas)

    # Filtrar resultados None e contar
    resultados = [r for r in resultados if r is not None]
    total = len(resultados)
    entregues = sum(1 for r in resultados if r['mensagem'] == "Entregue")
    falhas = sum(1 for r in resultados if r['mensagem'] == "Falha - Email retornou")

    print(f"\nVerificação concluída:")
    print(f"Total de máquinas: {total}")
    print(f"Entregues: {entregues}")
    print(f"Falhas: {falhas}")

    return resultados

async def exibir_menu():
    while True:
        print("\n=== Menu ===")
        print("1. Criar novas máquinas")
        print("2. Conectar a máquina existente")
        print("3. Sincronizar com API Linode")
        print("4. Verificar instalação do Docker em todas as máquinas")
        print("5. Deployar Docker em todas as máquinas")
        print("6. Verificar status do Postfix em todas as máquinas")
        print("7. Listar servidores ativos")
        print("8. Limpar banco de dados")
        print("9. Configurar VMs pendentes")
        print("10. Deletar VMs com erro")
        print("11. Deletar TODAS as Linodes com delay")
        print("0. Sair")

        opcao = input("\nEscolha uma opção: ")

        try:
            if opcao == "1":
                await criar_novas_maquinas()
            elif opcao == "2":
                await conectar_maquina_existente()
            elif opcao == "3":
                await sincronizar_banco_com_api()
            elif opcao == "4":
                await verificar_docker_todas_maquinas()
            elif opcao == "5":
                await deployar_docker_todas_maquinas()
            elif opcao == "6":
                await verificar_status_postfix_todas_maquinas()
            elif opcao == "7":
                await listar_servidores_ativos_menu()
            elif opcao == "8":
                await limpar_banco_dados()
            elif opcao == "9":
                await configurar_vms_pendentes()
            elif opcao == "10":
                deletar_vms_com_erro()
            elif opcao == "11":
                # Perguntar pelo delay desejado
                try:
                    delay = int(input("Digite o delay em segundos entre cada deleção (padrão: 5): ") or "5")
                    if delay < 0:
                        print("Delay não pode ser negativo. Usando valor padrão de 5 segundos.")
                        delay = 5
                    await deletar_todas_linodes_com_delay(delay)
                except ValueError:
                    print("Valor inválido. Usando valor padrão de 5 segundos.")
                    await deletar_todas_linodes_com_delay(5)
            elif opcao == "0":
                print("Encerrando programa...")
                break
            else:
                print("Opção inválida!")
        except Exception as e:
            print(f"Erro ao executar a opção {opcao}: {str(e)}")

async def main():
    init_db()
    await sincronizar_banco_com_api()
    await exibir_menu()

if __name__ == "__main__":
    asyncio.run(main())