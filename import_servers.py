import requests

headers = {
    'accept': 'application/json, text/plain, */*',
    'accept-language': 'en-US,en;q=0.9,pt;q=0.8',
    'authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.-Xc5zi_2_1RWzPAxRXAeQKqAZwFPmdYR4YDK4R-YRrQ',
    'cache-control': 'no-cache',
    'origin': 'https://app.usealpa.com',
    'pragma': 'no-cache',
    'priority': 'u=1, i',
    'referer': 'https://app.usealpa.com/',
    'sec-ch-ua': '"Not A(Brand";v="8", "Chromium";v="132", "Google Chrome";v="132"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Linux"',
    'sec-fetch-dest': 'empty',
    'sec-fetch-mode': 'cors',
    'sec-fetch-site': 'same-site',
    'user-agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
}

params = {
    'limit': '50',
    'offset': '0',
    'recipientId': '104937',
    'status': 'success',
}

response = requests.get('https://api.usealpa.com/v1/transfers', params=params, headers=headers)
print(response.json())
