#!/bin/bash
set -e

# Debug: Mostrar variáveis de ambiente recebidas
echo "Iniciando entrypoint.sh"
echo "SERVER_IP recebido: $SERVER_IP"

# Configurar hostname no formato exato solicitado
if [ -n "$SERVER_IP" ]; then
    # Formato exato: SERVER_IP com pontos substituídos por hífens + .ip.linodeusercontent.com
    HOSTNAME="${SERVER_IP//./-}.ip.linodeusercontent.com"
    echo "Configurando Postfix com hostname: $HOSTNAME"

    # Não tentamos mudar o hostname do sistema, apenas configuramos o Postfix

    # Configurar Postfix com o hostname exato
    echo "$HOSTNAME" > /etc/mailname
    postconf -e "myhostname = $HOSTNAME"
    postconf -e "myorigin = $HOSTNAME"
    postconf -e "smtp_helo_name = $HOSTNAME"
    postconf -e "mydestination = $HOSTNAME, localhost"

    # Adicionar entrada no /etc/hosts sem modificar o hostname do sistema
    if ! grep -q "$HOSTNAME" /etc/hosts; then
        echo "127.0.0.1 $HOSTNAME" >> /etc/hosts
    fi
else
    echo "ERRO: Variável SERVER_IP não definida. Hostname não pode ser configurado corretamente."
    exit 1
fi

# Create log file for postfix if it doesn't exist
touch /var/log/mail.log
chmod 644 /var/log/mail.log

# Reiniciar Postfix para aplicar as configurações
service postfix restart

# Verificar status do Postfix
echo "Verificando status do Postfix..."
service postfix status

# Verificar configuração do Postfix
echo "Verificando configuração do Postfix:"
echo "Postfix myhostname: $(postconf -h myhostname)"
echo "Postfix myorigin: $(postconf -h myorigin)"
echo "Postfix smtp_helo_name: $(postconf -h smtp_helo_name)"
echo "Conteúdo de /etc/mailname: $(cat /etc/mailname)"

# Create a symlink to mail.log to ensure our application can access it
if [ ! -f /media/veracrypt2/vpsDocker/mail.log ]; then
    mkdir -p /media/veracrypt2/vpsDocker
    ln -sf /var/log/mail.log /media/veracrypt2/vpsDocker/mail.log
fi

# Start the Node.js application with PM2
echo "Starting Node.js application..."
pm2-runtime start server.js