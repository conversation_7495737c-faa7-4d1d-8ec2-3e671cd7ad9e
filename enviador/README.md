# Docker Deploy Package

Este pacote contém todos os arquivos necessários para fazer o deploy da aplicação usando Docker.

## Arquivos Incluídos

- `Dockerfile`: Define a imagem Docker da aplicação
- `docker-compose.yml`: Configuração do ambiente Docker
- `entrypoint.sh`: Script de inicialização do container
- `.dockerignore`: Arquivos a serem ignorados no build
- `package.json`: Dependências Node.js
- `server.js`: Aplicação principal
- `api-enviador.tar.gz`: Imagem Docker pré-compilada

## Como Usar

1. Certifique-se de ter o Docker e Docker Compose instalados no servidor
2. Copie todos os arquivos desta pasta para o servidor
3. Execute o comando:
   ```bash
   docker load -i api-enviador.tar.gz
   docker-compose up -d
   ```

## Verificação

Após o deploy, a aplicação estará disponível na porta 80. Você pode verificar o status usando:

```bash
curl http://localhost/postfix-status
```

## Logs

Os logs da aplicação podem ser acessados usando:

```bash
docker logs api-enviador
```

## Estrutura de Diretórios

A aplicação cria automaticamente os seguintes diretórios:
- `logs/`: Para arquivos de log
- `uploads/`: Para arquivos enviados
- `uploads/html/`: Para arquivos HTML
- `uploads/emails/`: Para arquivos de email 