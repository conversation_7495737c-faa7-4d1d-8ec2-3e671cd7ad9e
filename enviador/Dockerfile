FROM ubuntu:22.04

ENV DEBIAN_FRONTEND=noninteractive

# Pré-configurações para evitar prompts - não definimos mailname aqui, será definido no entrypoint.sh
RUN echo "postfix postfix/main_mailer_type string Internet Site" | debconf-set-selections && \
    echo "postfix postfix/root_address string" | debconf-set-selections

# Instalação de pacotes
RUN apt-get update && apt-get install -y \
    lsof curl postfix cpanminus ufw sudo dnsutils \
    net-tools build-essential make apt-utils dialog \
    && rm -rf /var/lib/apt/lists/*

# Dependências Perl
RUN yes | cpanm File::Slurp

# Node.js via NVM
ENV NVM_DIR=/root/.nvm
ENV NODE_VERSION=20.18.0
RUN curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.1/install.sh | bash && \
    . "$NVM_DIR/nvm.sh" && \
    nvm install $NODE_VERSION && \
    nvm use $NODE_VERSION && \
    nvm alias default $NODE_VERSION
ENV PATH="/root/.nvm/versions/node/v$NODE_VERSION/bin:${PATH}"

# Configuração da aplicação
WORKDIR /app
COPY package.json server.js ./
RUN npm install && npm install -g pm2
RUN mkdir -p uploads/html uploads/emails logs
RUN chmod 777 server.js

# Configuração base do Postfix (sem definir hostname)
RUN postconf -e "inet_interfaces = all" && \
    postconf -e "inet_protocols = ipv4" && \
    postconf -e "maillog_file = /var/log/mail.log" && \
    postconf -e "mynetworks = *********/8 [::ffff:*********]/104 [::1]/128 **********/12 ***********/16" && \
    postconf -e "smtpd_banner = \$myhostname ESMTP" && \
    postconf -e "disable_vrfy_command = yes" && \
    postconf -e "smtp_helo_name = \$myhostname"

EXPOSE 80
COPY entrypoint.sh /entrypoint.sh
RUN chmod +x /entrypoint.sh
ENTRYPOINT ["/entrypoint.sh"]