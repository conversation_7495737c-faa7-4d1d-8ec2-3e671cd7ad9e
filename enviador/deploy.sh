#!/bin/bash

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Iniciando processo de deploy Docker...${NC}"

# Verificar se o Docker está instalado
if ! command -v docker &> /dev/null; then
    echo -e "${RED}Docker não está instalado. Instalando...${NC}"
    curl -fsSL https://get.docker.com -o get-docker.sh
    sudo sh get-docker.sh
    rm get-docker.sh
fi

# Verificar se o Docker Compose está instalado
if ! command -v docker-compose &> /dev/null; then
    echo -e "${RED}Docker Compose não está instalado. Instalando...${NC}"
    sudo curl -L "https://github.com/docker/compose/releases/download/v2.24.5/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
    sudo chmod +x /usr/local/bin/docker-compose
fi

# Obter o IP do servidor (do parâmetro ou do sistema)
SERVER_IP=${1:-$(hostname -I | awk '{print $1}')}
echo -e "${YELLOW}IP do servidor: $SERVER_IP${NC}"

# Verificar se o IP foi fornecido
if [ -z "$SERVER_IP" ]; then
    echo -e "${RED}ERRO: IP do servidor não fornecido ou não detectado${NC}"
    exit 1
fi

# Exportar SERVER_IP como variável de ambiente para o docker-compose
export SERVER_IP

# Mostrar o hostname que será configurado
HOSTNAME="${SERVER_IP//./-}.ip.linodeusercontent.com"
echo -e "${YELLOW}Hostname que será configurado: $HOSTNAME${NC}"

# Criar diretórios necessários
echo -e "${YELLOW}Criando diretórios necessários...${NC}"
mkdir -p logs uploads/html uploads/emails
chmod -R 777 logs uploads

# Verificar se estamos no diretório correto
if [ ! -f "Dockerfile" ] || [ ! -f "docker-compose.yml" ]; then
    echo -e "${RED}Arquivos Dockerfile ou docker-compose.yml não encontrados no diretório atual!${NC}"
    echo -e "${YELLOW}Conteúdo do diretório atual:${NC}"
    ls -la
    exit 1
fi

# Parar e remover container anterior se existir
echo -e "${YELLOW}Removendo container anterior se existir...${NC}"
docker rm -f api-enviador 2>/dev/null || true

# Construir e iniciar o container usando docker-compose com SERVER_IP explícito
echo -e "${YELLOW}Construindo e iniciando o container com SERVER_IP=$SERVER_IP...${NC}"
SERVER_IP=$SERVER_IP docker-compose build --no-cache
SERVER_IP=$SERVER_IP docker-compose up -d

# Verificar status
echo -e "${YELLOW}Verificando status do container...${NC}"
if docker ps | grep -q api-enviador; then
    echo -e "${GREEN}Container iniciado com sucesso!${NC}"

    # Verificar se a API está respondendo
    echo -e "${YELLOW}Verificando se a API está respondendo...${NC}"
    sleep 5 # Dar tempo para a API iniciar
    if curl -s http://localhost/postfix-status > /dev/null; then
        echo -e "${GREEN}API está respondendo corretamente!${NC}"
    else
        echo -e "${YELLOW}A API ainda não está respondendo. Aguardando mais 10 segundos...${NC}"
        sleep 10
        if curl -s http://localhost/postfix-status > /dev/null; then
            echo -e "${GREEN}API está respondendo corretamente após espera!${NC}"
        else
            echo -e "${RED}A API não está respondendo. Verifique os logs:${NC}"
            docker logs api-enviador
        fi
    fi
else
    echo -e "${RED}Falha ao iniciar o container. Verifique os logs:${NC}"
    docker logs api-enviador
    exit 1
fi

echo -e "${GREEN}Deploy concluído com sucesso!${NC}"
echo -e "${YELLOW}Para ver os logs, use: docker logs api-enviador${NC}"
echo -e "${GREEN}Aplicação disponível em: http://${SERVER_IP}${NC}"