version: '3'

services:
  api-enviador:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: api-enviador
    restart: always
    ports:
      - "80:80"
    volumes:
      - ./logs:/app/logs
      - ./uploads:/app/uploads
      - postfix-logs:/var/log
    environment:
      - NODE_ENV=production
      - SERVER_IP=${SERVER_IP}
    networks:
      - api-network

networks:
  api-network:
    driver: bridge

volumes:
  postfix-logs:
