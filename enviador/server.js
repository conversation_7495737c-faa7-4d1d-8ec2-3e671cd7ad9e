const express = require('express');
const fs = require('fs');
const path = require('path');
const { execSync, spawn } = require('child_process');
const multer = require('multer');
const nodemailer = require('nodemailer');
const { createLogger, transports, format } = require('winston');
const { v4: uuidv4 } = require('uuid');
const util = require('util');
const readFile = util.promisify(fs.readFile);
const { watch } = require('node:fs/promises');
const { createReadStream } = require('node:fs');
const { createInterface } = require('node:readline');
const { EventEmitter } = require('node:events');
const axios = require('axios');
const os = require('os');

const app = express();
exports.app = app;
const PORT = 80;
exports.PORT = PORT;

// Função para obter o hostname do Postfix
function getPostfixHostname() {
    try {
        // Tenta obter o hostname do sistema
        const hostname = os.hostname();

        // Se o hostname contiver pontos, substitui por hífens para o formato do Postfix
        if (hostname.includes('.')) {
            return hostname;
        }

        // Tenta obter o IP da máquina e formatar como hostname do Postfix
        const networkInterfaces = os.networkInterfaces();
        let ipAddress = '';

        // Procura por um endereço IPv4 não interno
        Object.keys(networkInterfaces).forEach((interfaceName) => {
            networkInterfaces[interfaceName].forEach((iface) => {
                if (iface.family === 'IPv4' && !iface.internal) {
                    ipAddress = iface.address;
                }
            });
        });

        if (ipAddress) {
            // Formata o IP como hostname do Postfix (substitui pontos por hífens)
            return ipAddress.replace(/\./g, '-') + '.ip.linodeusercontent.com';
        }

        // Se não conseguir obter o IP, tenta ler do arquivo de configuração do Postfix
        const postfixConfig = execSync('postconf -h myhostname').toString().trim();
        if (postfixConfig) {
            return postfixConfig;
        }

        // Fallback para o hostname do sistema
        return hostname;
    } catch (error) {
        console.error('Erro ao obter hostname do Postfix:', error);
        // Fallback para o hostname do sistema
        return os.hostname();
    }
}

// Função para obter o IP público usando axios
async function getPublicIP() {
    try {
        const response = await axios.get('https://ipv4.icanhazip.com/');
        return response.data.trim();
    } catch (error) {
        console.error('Erro ao obter IP público:', error);
        throw error; // Rejeita a promise para que a função chamadora saiba do erro
    }
}

// Função corrigida para configurar o Postfix para usar o remetente correto
async function configurePostfixSender(sender) {
    try {
        // Obtém o IP público
        const ip = await getPublicIP();
        const hostname = `${ip.replace(/\./g, '-')}.ip.linodeusercontent.com`;
        sender = `root@${hostname}`;

        // Configura o Postfix para usar o sender como envelope sender
        execSync(`postconf -e "sender_canonical_maps = regexp:/etc/postfix/sender_canonical"`);

        // Cria ou atualiza o arquivo de mapeamento de sender
        const senderCanonicalContent = `/^.*$/ ${sender}`;
        fs.writeFileSync('/etc/postfix/sender_canonical', senderCanonicalContent);

        // Recarrega a configuração do Postfix
        execSync('postfix reload');

        logger.info(`Postfix configurado para usar o remetente: ${sender}`);
        return true;
    } catch (error) {
        logger.error(`Erro ao configurar Postfix para usar o remetente: ${error.message}`, {
            stack: error.stack
        });
        return false;
    }
}

app.use(express.json());
app.use(express.urlencoded({ extended: true }));

const dataBrasileira = new Date().toLocaleDateString('pt-BR');

const POSTFIX_LOG_PATH = '/var/log/mail.log';

class LogWatcher extends EventEmitter {
  constructor(logPath) {
    super();
    this.logPath = logPath;
    this.filePosition = 0;
    this.isProcessing = false;
  }

  async start() {
    const watcher = watch(this.logPath);
    for await (const event of watcher) {
      if (event.eventType === 'change' && !this.isProcessing) {
        this.isProcessing = true;
        await this.processNewLines();
        this.isProcessing = false;
      }
    }
  }

  async processNewLines() {
    return new Promise((resolve, reject) => {
      const stream = createReadStream(this.logPath, {
        start: this.filePosition,
        encoding: 'utf8'
      });

      const rl = createInterface({
        input: stream,
        crlfDelay: Infinity
      });

      rl.on('line', (line) => {
        this.emit('line', line);
        this.filePosition += Buffer.byteLength(line + '\n', 'utf8');
      });

      stream.on('end', resolve);
      stream.on('error', reject);
    });
  }
}

const logWatcher = new LogWatcher(POSTFIX_LOG_PATH);
logWatcher.start();

const uploadHTML = multer({
    storage: multer.diskStorage({
        destination: (req, file, cb) => {
            const uploadDir = 'uploads/html';
            if (!fs.existsSync(uploadDir)) fs.mkdirSync(uploadDir, { recursive: true });
            cb(null, uploadDir);
        },
        filename: (req, file, cb) => cb(null, `${Date.now()}-${file.originalname}`)
    }),
    fileFilter: (req, file, cb) => {
        if (file.mimetype === 'text/html') cb(null, true);
        else cb(new Error('Apenas arquivos HTML são permitidos'), false);
    },
    limits: { fileSize: 5 * 1024 * 1024 } // 5MB
});

const uploadEmailList = multer({
    storage: multer.diskStorage({
        destination: (req, file, cb) => {
            const uploadDir = 'uploads/emails';
            if (!fs.existsSync(uploadDir)) fs.mkdirSync(uploadDir, { recursive: true });
            cb(null, uploadDir);
        },
        filename: (req, file, cb) => cb(null, `${Date.now()}-${file.originalname}`)
    }),
    fileFilter: (req, file, cb) => {
        const allowedMimeTypes = ['text/plain', 'text/csv'];
        const allowedExtensions = ['.txt', '.csv'];
        const fileExtension = path.extname(file.originalname).toLowerCase();

        if (allowedMimeTypes.includes(file.mimetype) && allowedExtensions.includes(fileExtension)) {
            cb(null, true);
        } else {
            cb(new Error('Apenas arquivos TXT ou CSV são permitidos'), false);
        }
    },
    limits: { fileSize: 100 * 1024 * 1024 } // 100MB
});

// Configuração do Postfix
const transporter = nodemailer.createTransport({
    sendmail: true,
    newline: 'unix',
    path: '/usr/sbin/sendmail',
    args: ['-f', '<EMAIL>'], // Será substituído pelo remetente real
    logger: true,
    debug: true
});

// Sistema de logging detalhado
const logger = createLogger({
    level: 'debug', // Alterado para debug para capturar mais informações
    format: format.combine(
        format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
        format.errors({ stack: true }),
        format.splat(),
        format.json()
    ),
    defaultMeta: { service: 'email-campaign' },
    transports: [
        new transports.File({
            filename: 'logs/campaigns.log',
            maxsize: 5242880, // 5MB
            maxFiles: 5
        }),
        new transports.Console({
            format: format.combine(
                format.colorize(),
                format.simple()
            )
        })
    ]
});
exports.logger = logger;

function checkRateLimit(email) {
    try {
        logger.debug('Verificando rate limit para:', { email });

        const logs = fs.readFileSync(POSTFIX_LOG_PATH, 'utf8');
        const recentLogs = logs.split('\n')
            .filter(line => line.includes(email))
            .slice(-3); // Pega últimos 3 registros

        logger.debug('Logs recentes encontrados:', {
            email,
            logs: recentLogs
        });

        const rateLimited = recentLogs.some(line =>
            line.includes('4.7.651') &&
            line.includes('temporarily rate limited')
        );

        if (rateLimited) {
            logger.warn('Rate limit detectado', {
                email,
                lastLogs: recentLogs
            });
            return { isLimited: true };
        }

        return { isLimited: false };
    } catch (error) {
        logger.error('Erro na verificação de rate limit', {
            error: error.message,
            stack: error.stack
        });
        return { isLimited: false };
    }
}

const fileStorage = {
    html: null,
    emails: null
};

const campaigns = new Map();

const replaceIfExists = (html, pattern, value) => {
    return html.includes(pattern)
        ? html.replace(new RegExp(pattern, 'g'), value)
        : html;
};

const parseLogs = (logs, messageIds) => {
    const statusMap = new Map();

    logs.split('\n').reverse().forEach(line => {
        messageIds.forEach(({ messageId, email }) => {
            if (line.includes(messageId)) {
                logger.debug('Analisando linha de log', {
                    messageId,
                    line
                });

                if (line.includes('status=sent')) {
                    logger.info(`Status: entregue para ${email}`);
                    statusMap.set(email, 'entregue');
                } else if (line.includes('status=bounced')) {
                    logger.warn(`Status: falha para ${email}`, { line });
                    statusMap.set(email, 'falha');
                } else if (line.includes('status=deferred')) {
                    logger.info(`Status: adiado para ${email}`, { line });
                    statusMap.set(email, 'adiado');
                } else if (line.includes('removed')) {
                    const status = line.includes('relay=')
                        ? 'provavelmente_entregue'
                        : 'status_incerto';
                    logger.debug(`Status especial para ${email}: ${status}`, { line });
                    statusMap.set(email, status);
                }
            }
        });
    });

    return statusMap;
};

function identifyListType(content) {
    const lines = content.split('\n').filter(line => line.trim() !== '');
    if (lines.length === 0) {
        throw new Error('Lista vazia');
    }

    const firstLine = lines[0];
    const columns = firstLine.split(',').map(col => col.trim().toLowerCase());

    // Verifica se é um cabeçalho
    const isHeader = columns.some(col => ['nome', 'email', 'cpf'].includes(col));

    // Se for um cabeçalho, verifica o formato
    if (isHeader) {
        if (columns.length === 3 && columns[0] === 'nome' && columns[1] === 'email' && columns[2] === 'cpf') {
            return 'nome,email,cpf';
        } else if (columns.length === 3 && columns[0] === 'cpf' && columns[1] === 'email' && columns[2] === 'especial') {
            return 'cpf,email,especial';
        } else if (columns.length === 2 && columns[0] === 'cpf' && columns[1] === 'email') {
            return 'cpf,email';
        }
    }

    // Se não for um cabeçalho ou não for um formato reconhecido, verifica se é uma lista de emails
    // Verifica a primeira linha que não é cabeçalho
    const dataLine = isHeader && lines.length > 1 ? lines[1] : lines[0];
    const dataColumns = dataLine.split(',');

    // Verifica se alguma coluna contém um email
    const emailRegex = /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/;

    // Verifica o formato nome,email,cpf (sem cabeçalho)
    if (dataColumns.length === 3) {
        // Verifica se a segunda coluna contém um email
        if (emailRegex.test(dataColumns[1])) {
            // Verifica se a terceira coluna parece um CPF (números)
            const cpfRegex = /^\d+$/;
            if (cpfRegex.test(dataColumns[2].trim())) {
                logger.debug('Detectado formato nome,email,cpf sem cabeçalho', {
                    nome: dataColumns[0],
                    email: dataColumns[1],
                    cpf: dataColumns[2]
                });
                return 'nome,email,cpf';
            }
        }
    }

    if (dataColumns.length === 1 && emailRegex.test(dataColumns[0])) {
        // Lista com apenas emails
        return 'email';
    } else if (dataColumns.length >= 2) {
        // Verifica se alguma coluna contém um email
        for (let i = 0; i < dataColumns.length; i++) {
            if (emailRegex.test(dataColumns[i])) {
                // Se a primeira coluna contém nome e a segunda email
                if (i === 1 && !emailRegex.test(dataColumns[0])) {
                    return 'nome,email,cpf'; // Assumimos que é nome,email,cpf mesmo se não tiver CPF
                } else if (i === 0 && dataColumns.length >= 2) {
                    return 'email'; // Se o email está na primeira coluna, tratamos como lista de emails
                }
            }
        }
    }

    // Se chegou aqui, tenta verificar se há emails em qualquer linha
    for (const line of lines) {
        if (emailRegex.test(line)) {
            return 'email';
        }
    }

    // Verifica se todas as linhas têm o mesmo número de colunas (3) e se a segunda coluna parece um email
    if (lines.length >= 2) {
        const allLinesHaveThreeColumns = lines.every(line => line.split(',').length === 3);
        const secondColumnIsEmail = lines.every(line => {
            const cols = line.split(',');
            return cols.length === 3 && emailRegex.test(cols[1].trim());
        });

        if (allLinesHaveThreeColumns && secondColumnIsEmail) {
            logger.debug('Detectado formato nome,email,cpf baseado em múltiplas linhas');
            return 'nome,email,cpf';
        }
    }

    throw new Error('Formato de lista não reconhecido');
}

async function consultarCPF(cpf) {
    const url = `http://*************/api/simples/consultar-cpf?cpf=${cpf}&apikey=c4085f0de4aca21e6ea1a7ecb71ca560`;
    try {
        const response = await axios.get(url);
        if (response.data && response.data.length > 0) {
            return response.data[0].NOME; // Retorna o nome do primeiro resultado
        } else {
            logger.warn('CPF não encontrado na API', { cpf });
            return null; // Retorna null se o CPF não for encontrado
        }
    } catch (error) {
        logger.error('Erro ao consultar CPF na API', {
            cpf,
            error: error.response?.data || error.message
        });
        return null; // Retorna null em caso de erro
    }
}

// Endpoints
app.post('/upload-html', uploadHTML.single('html'), (req, res) => {
    try {
        if (!req.file) throw new Error('Nenhum arquivo recebido');

        fileStorage.html = {
            path: req.file.path,
            name: req.file.originalname,
            uploadedAt: new Date(),
            size: req.file.size,
            mimeType: req.file.mimetype
        };

        res.json({
            success: true,
            message: 'HTML armazenado com sucesso',
            details: {
                location: fileStorage.html.path,
                originalName: fileStorage.html.name,
                size: fileStorage.html.size
            }
        });

    } catch (error) {
        logger.error(`Erro no upload de HTML: ${error.message}`, {
            stack: error.stack,
            receivedFile: req.file ? {
                originalname: req.file.originalname,
                mimetype: req.file.mimetype,
                size: req.file.size
            } : null
        });

        res.status(500).json({
            error: error.message,
            solution: 'Verifique: 1) Formulário com enctype="multipart/form-data" 2) Arquivo HTML válido'
        });
    }
});

app.post('/upload-list', uploadEmailList.single('emails'), async (req, res) => {
    try {
        if (!req.file) throw new Error('Nenhum arquivo recebido');

        // Validação adicional da extensão
        const ext = path.extname(req.file.originalname).toLowerCase();
        if (!['.txt', '.csv'].includes(ext)) {
            fs.unlinkSync(req.file.path);
            throw new Error('Formato inválido. Use .txt ou .csv');
        }

        const content = await readFile(req.file.path, 'utf8');
        const listType = identifyListType(content);

        fileStorage.emails = {
            path: req.file.path,
            name: req.file.originalname,
            uploadedAt: new Date(),
            size: req.file.size,
            mimeType: req.file.mimetype,
            listType
        };

        res.json({
            success: true,
            message: 'Lista de emails armazenada',
            details: {
                location: fileStorage.emails.path,
                originalName: fileStorage.emails.name,
                entries: content.split('\n').length,
                listType
            }
        });

    } catch (error) {
        logger.error(`Erro no upload de emails: ${error.message}`, {
            stack: error.stack,
            receivedFile: req.file ? {
                originalname: req.file.originalname,
                mimetype: req.file.mimetype,
                size: req.file.size
            } : null
        });

        res.status(500).json({
            error: error.message,
            solution: 'Verifique: 1) Extensão .txt/.csv 2) Formato da lista (nome,email,cpf; cpf,email; cpf,email,especial; ou somente email)\n\nExemplos de formatos suportados:\n- nome,email,cpf\nELOI CARLOS,<EMAIL>,28381763827\n\n- Apenas emails:\<EMAIL>\<EMAIL>'
        });
    }
});

app.post('/start-campaign', async (req, res) => {
    try {
        const { senders = [], subjects = [] } = req.body;

        // Validação rigorosa dos parâmetros
        if (!Array.isArray(senders) || senders.length === 0 || senders.length > 10) {
            throw new Error('Forneça entre 1 e 10 remetentes válidos');
        }

        if (!Array.isArray(subjects) || subjects.length === 0 || subjects.length > 10) {
            throw new Error('Forneça entre 1 e 10 assuntos válidos');
        }

        // Garantir que os remetentes estejam no formato correto
        // A função sendEmail formatará o remetente como "SENDER_VARIAVEL <HOSTNAME_POSTFIX>"
        const cleanedSenders = senders.map(sender => {
            // Remove qualquer parte entre < e > para usar apenas o nome do remetente
            return sender.replace(/<[^>]+>/g, '').trim();
        });

        // Verificação detalhada dos arquivos necessários
        if (!fileStorage.html && !fileStorage.emails) {
            throw new Error('Faça upload do HTML e da lista de emails primeiro. Ambos estão faltando.');
        } else if (!fileStorage.html) {
            throw new Error('Faça upload do arquivo HTML primeiro. A lista de emails já foi carregada.');
        } else if (!fileStorage.emails) {
            throw new Error('Faça upload da lista de emails primeiro. O arquivo HTML já foi carregado.');
        }

        // Verificação adicional da validade dos arquivos
        if (fileStorage.html && (!fileStorage.html.path || !fs.existsSync(fileStorage.html.path))) {
            throw new Error('Arquivo HTML inválido ou não encontrado. Faça o upload novamente.');
        }

        if (fileStorage.emails && (!fileStorage.emails.path || !fs.existsSync(fileStorage.emails.path))) {
            throw new Error('Lista de emails inválida ou não encontrada. Faça o upload novamente.');
        }

        const [htmlContent, rawEmails] = await Promise.all([
            readFile(fileStorage.html.path, 'utf8'),
            readFile(fileStorage.emails.path, 'utf8')
        ]);

        const emails = rawEmails.split('\n')
            .map(e => e.trim())
            .filter(e => e !== '');

        if (emails.length === 0) {
            throw new Error('Lista de emails inválida ou vazia após filtragem');
        }

        const campaignId = uuidv4();
        const campaign = {
            id: campaignId,
            status: 'executando',
            startTime: new Date(),
            config: {
                senders: cleanedSenders.slice(0, 10),
                subjects: subjects.slice(0, 10),
                totalEmails: emails.length
            },
            stats: {
                total: emails.length,
                enviados: 0,
                falhas: 0,
                adiados: 0,
                pendentes: emails.length
            },
            messageIds: [],
            logs: [],
            listType: fileStorage.emails.listType
        };

        campaigns.set(campaignId, campaign);
        processCampaign(campaignId, htmlContent, emails);
        res.json({
            success: true,
            campaignId,
            message: 'Campanha iniciada com sucesso',
            detalhes: {
                senders_utilizados: campaign.config.senders,
                subjects_utilizados: campaign.config.subjects,
                total_emails: emails.length,
                inicio: campaign.startTime.toISOString(),
                previsao_conclusao: new Date(Date.now() + (emails.length * 2000)).toISOString(),
                listType: campaign.listType
            }
        });

    } catch (error) {
        logger.error(`Erro ao iniciar campanha: ${error.message}`, {
            stack: error.stack,
            body: req.body
        });

        // Determinar a solução com base no tipo de erro
        let solution = 'Verifique: 1) Formato dos remetentes 2) Formato dos assuntos 3) Arquivos de upload válidos';

        if (error.message.includes('Faça upload do HTML e da lista de emails primeiro. Ambos estão faltando.')) {
            solution = 'Você precisa fazer upload tanto do arquivo HTML quanto da lista de emails antes de iniciar a campanha. Use os endpoints /upload-html e /upload-list.';
        } else if (error.message.includes('Faça upload do arquivo HTML primeiro')) {
            solution = 'Você já fez upload da lista de emails, mas ainda precisa fazer upload do arquivo HTML. Use o endpoint /upload-html.';
        } else if (error.message.includes('Faça upload da lista de emails primeiro')) {
            solution = 'Você já fez upload do arquivo HTML, mas ainda precisa fazer upload da lista de emails. Use o endpoint /upload-list.';
        } else if (error.message.includes('Arquivo HTML inválido ou não encontrado')) {
            solution = 'O arquivo HTML que você enviou anteriormente não foi encontrado ou está corrompido. Faça o upload novamente usando o endpoint /upload-html.';
        } else if (error.message.includes('Lista de emails inválida ou não encontrada')) {
            solution = 'A lista de emails que você enviou anteriormente não foi encontrada ou está corrompida. Faça o upload novamente usando o endpoint /upload-list.';
        } else if (error.message.includes('Forneça entre 1 e 10 remetentes válidos')) {
            solution = 'Você deve fornecer entre 1 e 10 remetentes válidos no formato de array. Exemplo: {"senders": ["Receita Federal", "Governo Federal"]}';
        } else if (error.message.includes('Forneça entre 1 e 10 assuntos válidos')) {
            solution = 'Você deve fornecer entre 1 e 10 assuntos válidos no formato de array. Exemplo: {"subjects": ["Assunto 1", "Assunto 2"]}';
        } else if (error.message.includes('Lista de emails inválida ou vazia')) {
            solution = 'A lista de emails que você enviou está vazia ou contém apenas linhas em branco. Verifique o conteúdo do arquivo e faça o upload novamente.';
        }

        // Adicionar informações sobre o estado atual dos uploads
        const uploadStatus = {
            html: fileStorage.html ? {
                nome: fileStorage.html.name,
                tamanho: fileStorage.html.size,
                carregado_em: fileStorage.html.uploadedAt
            } : null,
            emails: fileStorage.emails ? {
                nome: fileStorage.emails.name,
                tamanho: fileStorage.emails.size,
                carregado_em: fileStorage.emails.uploadedAt,
                tipo_lista: fileStorage.emails.listType
            } : null
        };

        res.status(500).json({
            error: error.message,
            solution: solution,
            upload_status: uploadStatus
        });
    }
});

function formatarCPF(cpf) {
    if (!cpf || typeof cpf !== 'string') return ''; // Retorna vazio se o CPF for inválido

    // Remove caracteres não numéricos
    cpf = cpf.replace(/\D/g, '');

    // Formata o CPF no padrão XXX.XXX.XXX-XX
    if (cpf.length === 11) {
        return cpf.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, '$1.$2.$3-$4');
    }

    return cpf; // Retorna o CPF sem formatação se não tiver 11 dígitos
}

async function processCampaign(campaignId, htmlContent, emails) {
    const campaign = campaigns.get(campaignId);
    const { senders, subjects } = campaign.config;
    let successfulEmails = [];
    let failedEmails = [];
    const RATE_LIMIT_WAIT = 3600000; // 1 hora
    const MAX_CONCURRENT_CONNECTIONS = 1; // Alterado para 1 para enviar um email por vez

    try {
        const batchSize = 1; // Alterado para 1 para enviar um email por vez
        const delayBetweenEmails = 1000; // 1 segundo de delay entre cada email
        let currentIndex = 0;

        while (currentIndex < emails.length) {
            const batch = emails.slice(currentIndex, currentIndex + batchSize);

            logger.info(`Processando lote ${Math.floor(currentIndex/batchSize) + 1} de ${Math.ceil(emails.length/batchSize)}`, {
                campaignId,
                batchSize: batch.length,
                totalProcessed: currentIndex
            });

            // Processa em grupos de MAX_CONCURRENT_CONNECTIONS
            for (let i = 0; i < batch.length; i += MAX_CONCURRENT_CONNECTIONS) {
                const connectionBatch = batch.slice(i, i + MAX_CONCURRENT_CONNECTIONS);

                const results = await Promise.allSettled(
                    connectionBatch.map(async (entry) => {
                        let email, nome, cpf, especial;
                        const parts = entry.split(',').map(part => part.trim());
                        const emailRegex = /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/;

                        if (campaign.listType === 'nome,email,cpf') {
                            // Formato: nome,email,cpf
                            if (parts.length >= 3) {
                                nome = parts[0];
                                email = parts[1];
                                cpf = parts[2];

                                // Verifica se o email é válido
                                if (!emailRegex.test(email)) {
                                    // Tenta encontrar um email em qualquer parte da entrada
                                    for (const part of parts) {
                                        if (emailRegex.test(part)) {
                                            email = part;
                                            break;
                                        }
                                    }
                                }

                                // Se o nome estiver vazio, tenta consultar pelo CPF
                                if (cpf && (!nome || nome.trim() === '')) {
                                    nome = await consultarCPF(cpf);
                                }
                            } else if (parts.length === 2) {
                                // Caso tenha apenas nome e email
                                nome = parts[0];
                                email = parts[1];

                                // Verifica se o email é válido
                                if (!emailRegex.test(email)) {
                                    // Tenta encontrar um email em qualquer parte da entrada
                                    for (const part of parts) {
                                        if (emailRegex.test(part)) {
                                            email = part;
                                            break;
                                        }
                                    }
                                }
                            } else if (parts.length === 1 && emailRegex.test(parts[0])) {
                                // Se só tiver uma parte e for um email
                                email = parts[0];
                                nome = email.split('@')[0];
                            }

                            logger.debug('Processando entrada nome,email,cpf:', {
                                nome,
                                email,
                                cpf
                            });
                        } else if (campaign.listType === 'cpf,email') {
                            // Formato: cpf,email
                            if (parts.length >= 2) {
                                cpf = parts[0];
                                email = parts[1];
                                nome = await consultarCPF(cpf);

                                // Verifica se o email é válido
                                if (!emailRegex.test(email)) {
                                    // Tenta encontrar um email em qualquer parte da entrada
                                    for (const part of parts) {
                                        if (emailRegex.test(part)) {
                                            email = part;
                                            break;
                                        }
                                    }
                                }
                            } else if (parts.length === 1 && emailRegex.test(parts[0])) {
                                // Se só tiver uma parte e for um email
                                email = parts[0];
                            }

                            logger.debug('Processando entrada cpf,email:', {
                                cpf,
                                email,
                                nome
                            });
                        } else if (campaign.listType === 'cpf,email,especial') {
                            // Formato: cpf,email,especial
                            if (parts.length >= 3) {
                                cpf = parts[0];
                                email = parts[1];
                                especial = parts[2];
                                nome = await consultarCPF(cpf);

                                // Verifica se o email é válido
                                if (!emailRegex.test(email)) {
                                    // Tenta encontrar um email em qualquer parte da entrada
                                    for (const part of parts) {
                                        if (emailRegex.test(part)) {
                                            email = part;
                                            break;
                                        }
                                    }
                                }
                            } else if (parts.length === 2) {
                                cpf = parts[0];
                                email = parts[1];
                                nome = await consultarCPF(cpf);

                                // Verifica se o email é válido
                                if (!emailRegex.test(email)) {
                                    // Tenta encontrar um email em qualquer parte da entrada
                                    for (const part of parts) {
                                        if (emailRegex.test(part)) {
                                            email = part;
                                            break;
                                        }
                                    }
                                }
                            } else if (parts.length === 1 && emailRegex.test(parts[0])) {
                                // Se só tiver uma parte e for um email
                                email = parts[0];
                            }

                            logger.debug('Processando entrada cpf,email,especial:', {
                                cpf,
                                email,
                                nome,
                                especial
                            });
                        } else if (campaign.listType === 'email') {
                            // Formato: apenas email
                            if (parts.length >= 1) {
                                // Procura por um email em qualquer parte da entrada
                                for (const part of parts) {
                                    if (emailRegex.test(part)) {
                                        email = part;
                                        break;
                                    }
                                }

                                // Se não encontrou um email, usa a entrada completa
                                if (!email && emailRegex.test(entry)) {
                                    email = entry;
                                }
                            }

                            // Se ainda não tiver um email, usa a entrada completa
                            if (!email) {
                                email = entry;
                            }
                        }

                        // Verificação final para garantir que temos um email válido
                        if (!email || !emailRegex.test(email)) {
                            logger.warn('Email inválido ou não encontrado na entrada', { entry });
                            throw new Error(`Email inválido ou não encontrado: ${entry}`);
                        }

                        const customizedContent = customizeEmail(email, htmlContent, nome, cpf, especial);
                        // Selecionamos um remetente aleatório da lista
                        // A função sendEmail formatará o remetente como "SENDER_VARIAVEL <HOSTNAME_POSTFIX>"
                        const selectedSender = senders[Math.floor(Math.random() * senders.length)];
                        return sendEmail(email, customizedContent, selectedSender, customizeSubject(subjects));
                    })
                );

                for (const result of results) {
                    if (result.status === 'fulfilled') {
                        logger.debug('Email processado com sucesso', {
                            email: result.value.email,
                            messageId: result.value.messageId
                        });
                        campaign.stats.enviados++;
                        campaign.messageIds.push({
                            messageId: result.value.messageId,
                            email: result.value.email
                        });
                        successfulEmails.push(result.value.email);
                    } else {
                        logger.error('Falha no envio de email', {
                            email: result.reason.email || 'Email desconhecido',
                            reason: result.reason.error?.message || result.reason.error || 'Erro desconhecido',
                            rateLimited: result.reason.rateLimited || false,
                            stack: result.reason.stack
                        });
                        campaign.stats.falhas++;
                        failedEmails.push(result.reason.email || 'Email desconhecido');
                    }
                }

                // Pequeno delay entre cada email
                await new Promise(resolve => setTimeout(resolve, delayBetweenEmails));
            }

            // Verifica rate limit após o envio do batch
            let lastEmailInBatch;
            const lastEntry = batch[batch.length - 1];

            if (lastEntry) {
                const parts = lastEntry.split(',').map(part => part.trim());
                const emailRegex = /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/;

                // Extrai o email com base no tipo de lista
                if (campaign.listType === 'nome,email,cpf' && parts.length >= 2) {
                    lastEmailInBatch = parts[1]; // Email está na segunda posição

                    // Verifica se o email é válido
                    if (!emailRegex.test(lastEmailInBatch)) {
                        // Tenta encontrar um email em qualquer parte da entrada
                        for (const part of parts) {
                            if (emailRegex.test(part)) {
                                lastEmailInBatch = part;
                                break;
                            }
                        }
                    }
                } else if (campaign.listType === 'cpf,email' && parts.length >= 2) {
                    lastEmailInBatch = parts[1]; // Email está na segunda posição

                    // Verifica se o email é válido
                    if (!emailRegex.test(lastEmailInBatch)) {
                        // Tenta encontrar um email em qualquer parte da entrada
                        for (const part of parts) {
                            if (emailRegex.test(part)) {
                                lastEmailInBatch = part;
                                break;
                            }
                        }
                    }
                } else if (campaign.listType === 'cpf,email,especial' && parts.length >= 2) {
                    lastEmailInBatch = parts[1]; // Email está na segunda posição

                    // Verifica se o email é válido
                    if (!emailRegex.test(lastEmailInBatch)) {
                        // Tenta encontrar um email em qualquer parte da entrada
                        for (const part of parts) {
                            if (emailRegex.test(part)) {
                                lastEmailInBatch = part;
                                break;
                            }
                        }
                    }
                } else if (campaign.listType === 'email') {
                    // Procura por um email em qualquer parte da entrada
                    for (const part of parts) {
                        if (emailRegex.test(part)) {
                            lastEmailInBatch = part;
                            break;
                        }
                    }

                    // Se não encontrou um email, usa a entrada completa
                    if (!lastEmailInBatch && emailRegex.test(lastEntry)) {
                        lastEmailInBatch = lastEntry;
                    }
                }

                // Se ainda não encontrou um email válido, tenta extrair usando regex
                if (!lastEmailInBatch || !emailRegex.test(lastEmailInBatch)) {
                    const match = lastEntry.match(emailRegex);
                    if (match) {
                        lastEmailInBatch = match[0];
                        logger.debug('Email extraído usando regex', {
                            original: lastEntry,
                            extracted: lastEmailInBatch
                        });
                    } else {
                        lastEmailInBatch = lastEntry; // Usa a entrada completa como fallback
                    }
                }

                const rateLimit = checkRateLimit(lastEmailInBatch);
                if (rateLimit.isLimited) {
                    logger.warn(`Rate limit detectado. Pausando campanha por 1 hora`, {
                        campaignId,
                        rateLimitInfo: rateLimit,
                        lastEmailChecked: lastEmailInBatch
                    });

                    campaign.status = 'rate_limited';
                    campaigns.set(campaignId, campaign);
                    await new Promise(resolve => setTimeout(resolve, RATE_LIMIT_WAIT));

                    logger.info(`Retomando campanha após pausa de rate limit`, {
                        campaignId,
                        resumingFrom: currentIndex
                    });

                    campaign.status = 'executando';
                    campaigns.set(campaignId, campaign);
                    continue;
                }
            }

            currentIndex += batchSize;
            campaign.stats.pendentes = emails.length - (campaign.stats.enviados + campaign.stats.falhas);
            campaigns.set(campaignId, campaign);
        }

        campaign.status = 'concluída';
        campaign.endTime = new Date();
        logger.info(`Campanha finalizada: ${campaignId}`);

    } catch (error) {
        campaign.status = 'falhou';
        campaign.endTime = new Date();
        logger.error(`Falha na campanha: ${error.message}`, {
            campaignId,
            error: error.stack,
            details: error.response?.data || error.message
        });
    } finally {
        campaign.successfulEmails = successfulEmails;
        campaign.failedEmails = failedEmails;
        campaigns.set(campaignId, campaign);
    }
}

function customizeEmail(email, htmlContent, nome, cpf, especial) {
    const random3dig = () => Math.floor(Math.random() * 900 + 100).toString();
    const firstName = nome ? nome.split(' ')[0] : email.split('@')[0].replace(/[^a-zA-Z]/g, '');
    const r55 = random3dig();

    const cpfFormatado = formatarCPF(cpf);

    let html = htmlContent;
    const replacements = {
        '%email%': email,
        '%primeironome%': firstName,
        '%nome%': nome,
        '%cpf%': cpfFormatado || '',
        '%especial%': especial || '',
        '%rand1%': random3dig(),
        '%rand2%': random3dig(),
        '%rand3%': random3dig(),
        '%rand4%': random3dig(),
        '%rand55%': r55,
        '{{ randomness }}': dataBrasileira,
        '%%data_atual%%': dataBrasileira
    };

    Object.entries(replacements).forEach(([pattern, value]) => {
        html = replaceIfExists(html, pattern, value);
    });

    return html;
}

function customizeSubject(subjects) {
    const r55 = Math.floor(Math.random() * 900 + 100).toString();
    const baseSubject = subjects[Math.floor(Math.random() * subjects.length)];
    return baseSubject.replace(/%rand55%/g, r55);
}

app.get('/queue-status', async (_, res) => {
    try {
        const queueStats = execSync('postqueue -p').toString();
        const deferredCount = parseInt(execSync('find /var/spool/postfix/deferred -type f | wc -l').toString().trim());
        const logs = await readFile(POSTFIX_LOG_PATH, 'utf8');
        const throughput = calculateThroughput(logs);

        res.json({
            activeQueue: parseQueue(queueStats),
            deferred: deferredCount,
            throughput: throughput
        });

    } catch (error) {
        logger.error(`Erro ao obter status da fila: ${error.message}`, {
            stack: error.stack
        });

        res.status(500).json({
            error: 'Erro ao obter status da fila',
            solution: 'Verifique: 1) Permissões de execução 2) Caminhos de arquivos'
        });
    }
});

function parseQueue(queueStats) {
    const lines = queueStats.split('\n');
    const queueInfo = {
        total: 0,
        active: 0,
        deferred: 0,
        sizes: []
    };

    lines.forEach(line => {
        if (line.startsWith('-Queue ID-')) {
            queueInfo.total = parseInt(line.split(' ')[2]);
        } else if (line.startsWith('A')) {
            queueInfo.active++;
        } else if (line.startsWith('D')) {
            queueInfo.deferred++;
        } else if (line.match(/^\s+\d+\s+KB/)) {
            queueInfo.sizes.push(parseInt(line.trim().split(' ')[0]));
        }
    });

    return queueInfo;
}

function calculateThroughput(logs) {
    const now = new Date();
    const oneMinuteAgo = new Date(now.getTime() - 60000);
    const oneHourAgo = new Date(now.getTime() - 3600000);

    const sentLogs = logs.split('\n').filter(log => log.includes('status=sent'));
    const sentInLastMinute = sentLogs.filter(log => new Date(log.split(' ')[0]) > oneMinuteAgo).length;
    const sentInLastHour = sentLogs.filter(log => new Date(log.split(' ')[0]) > oneHourAgo).length;

    return {
        perMinute: sentInLastMinute,
        perHour: sentInLastHour
    };
}

app.get('/campanha/:id', async (req, res) => {
    try {
        const campaignId = req.params.id;
        const campaign = campaigns.get(campaignId);

        if (!campaign) {
            logger.error(`Campanha não encontrada: ${campaignId}`);
            return res.status(404).json({
                error: 'Campanha não encontrada',
                solution: 'Verifique o ID da campanha e tente novamente.'
            });
        }

        // Configura listener para atualizações em tempo real
        const listener = (line) => {
            const match = line.match(/postfix\/smtp\[\d+\]: (\w+): status=(\w+)/);
            if (match && campaign.messageIds.some(msg => msg.messageId === match[1])) {
                updateCampaignStats(campaign, match[2]);
            }
        };

        logWatcher.on('line', listener);

        // Processa logs existentes imediatamente
        await logWatcher.processNewLines();

        // Remove listener após processamento
        logWatcher.off('line', listener);

        // Prepara resposta no formato exigido
        res.json({
            id: campaign.id,
            status: campaign.status,
            inicio: campaign.startTime.toISOString(),
            termino: campaign.endTime?.toISOString() || null,
            configuracao: campaign.config,
            estatisticas: {
                total: campaign.stats.total,
                enviados: campaign.stats.enviados,
                falhas: campaign.stats.falhas,
                adiados: campaign.stats.adiados,
                pendentes: campaign.stats.pendentes
            },
            detalhes_status: {
                emails_processados: campaign.messageIds.length,
                ultima_atualizacao: new Date().toISOString()
            }
        });

    } catch (error) {
        logger.error(`Erro na campanha ${req.params.id}: ${error.message}`);
        res.status(500).json({
            error: error.message,
            solucao: 'Verifique: 1) ID da campanha 2) Status do serviço de logs'
        });
    }
});

// Rota para configurar o remetente padrão do Postfix
app.post('/configure-sender', (req, res) => {
    try {
        const { sender } = req.body;

        if (!sender || typeof sender !== 'string') {
            return res.status(400).json({
                success: false,
                message: 'Forneça um remetente válido',
                example: 'Receita Master'
            });
        }

        // Configurar o Postfix para usar o remetente
        const hostname = getPostfixHostname();
        const senderEmail = `${sender.replace(/[^a-zA-Z0-9]/g, '')}@${hostname}`;
        const success = configurePostfixSender(senderEmail);

        if (success) {
            return res.json({
                success: true,
                message: `Remetente configurado com sucesso: ${sender} <${hostname}>`,
                details: {
                    sender,
                    hostname,
                    senderEmail
                }
            });
        } else {
            return res.status(500).json({
                success: false,
                message: 'Falha ao configurar o remetente',
                solution: 'Verifique os logs do servidor'
            });
        }
    } catch (error) {
        logger.error(`Erro ao configurar remetente: ${error.message}`, {
            stack: error.stack
        });

        return res.status(500).json({
            success: false,
            message: `Erro ao configurar remetente: ${error.message}`,
            solution: 'Verifique os logs do servidor'
        });
    }
});

app.get('/postfix-status', (_, res) => {
    let postfixStatus = 'desconhecido';
    let emailStatus = 'Desconhecido';
    let logOutput = '';
    const logPath = "/var/log/mail.log";
    const emailIdentifier = "<EMAIL>";

    const bounceCodes = {
        '4.2.0': 'Problema temporário de entrega',
        '4.2.1': 'Caixa postal cheia temporariamente',
        '4.2.2': 'Limite de cota excedido',
        '5.0.0': 'Erro geral de entrega',
        '5.1.0': 'Erro genérico de endereço',
        '5.1.1': 'Endereço de email inválido',
        '5.1.2': 'Domínio inexistente',
        '5.1.3': 'Destinatário inválido',
        '5.1.4': 'Endereço ambíguo',
        '5.1.6': 'Domínio desativado',
        '5.2.0': 'Caixa postal indisponível',
        '5.2.1': 'Email bloqueado pelo receptor',
        '5.2.2': 'Caixa postal cheia permanentemente',
        '5.3.4': 'Mensagem muito grande',
        '5.4.0': 'Erro de roteamento',
        '5.4.4': 'Problema de configuração DNS',
        '5.5.0': 'Erro de protocolo SMTP',
        '5.7.1': 'Bloqueio por política de segurança',
        '5.7.13': 'Falha de autenticação',
        '5.7.25': 'Falha na verificação DKIM/DMARC'
    };

    // Função para extrair informações do bounce a partir de uma linha de log
    function parseBounce(logLine) {
        const bounceMatch = logLine.match(/status=bounced \((\d+\.\d+\.\d+)/);
        if (!bounceMatch) return null;
        const code = bounceMatch[1];
        return {
            code: code,
            reason: bounceCodes[code] || 'Motivo não especificado',
            description: `Código SMTP ${code}: ${bounceCodes[code] || 'Erro de entrega não catalogado'}`
        };
    }

    // Inicia o processo para verificar o status do Postfix
    const postfixProcess = spawn('postfix', ['status']);

    postfixProcess.stdout.on('data', (data) => {
        const output = data.toString();
        if (output.includes('postfix/postfix-script: the Postfix mail system is running')) {
            postfixStatus = 'executando';
        } else {
            postfixStatus = 'parado';
        }
    });

    postfixProcess.on('error', (error) => {
        logger.error('Erro ao executar o comando "postfix status":', { error });
        return res.status(500).json({ status: 'error', message: 'Erro ao executar o comando "postfix status"' });
    });

    // Após o término do processo do postfix, dispara um envio de teste para gerar logs
    postfixProcess.on('close', () => {
        const sendmailProcess = spawn('sendmail', [emailIdentifier]);
        sendmailProcess.stdin.write("Assunto: Assunto do email\n\nCorpo do email aqui.");
        sendmailProcess.stdin.end();

        sendmailProcess.on('error', (error) => {
            logger.error('Erro durante o envio de teste via sendmail:', { error });
            return res.status(500).json({ status: 'error', message: 'Erro durante o envio de teste via sendmail' });
        });

        sendmailProcess.on('close', () => {
            try {
                if (fs.existsSync(logPath)) {
                    fs.readFile(logPath, 'utf8', (err, logs) => {
                        if (err) {
                            logger.error('Erro ao ler logs do Postfix', { error: err });
                            return res.status(500).json({ status: 'error', message: 'Erro ao ler logs', error: err.message });
                        }

                        const emailLogs = logs.split('\n').filter(line => line.includes(emailIdentifier));
                        logOutput = emailLogs.join('\n');
                        let bounceDetails = null;
                        const bounceLine = emailLogs.find(line => line.includes('status=bounced'));

                        if (bounceLine) {
                            bounceDetails = parseBounce(bounceLine);
                            emailStatus = 'Falha - Email retornou';
                        } else if (logs.includes('status=sent')) {
                            emailStatus = 'Entregue';
                        } else if (logs.includes('IP blacklistado.')) {
                            emailStatus = 'Falha - Email retornou';
                        } else if (logs.includes('relay=hotmail-com.olc.protection.outlook.com')) {
                            emailStatus = 'Em processamento';
                        } else if (logs.includes('delivery temporarily suspended')) {
                            emailStatus = 'Limite temporário atingido';
                        } else {
                            emailStatus = 'Não encontrado nos logs';
                        }

                        res.json({
                            status: 'success',
                            postfixStatus: postfixStatus,
                            emailStatus: emailStatus,
                            diagnostics: {
                                bounceDetails: bounceDetails,
                                logAnalysis: {
                                    totalEntries: emailLogs.length,
                                    firstEntry: emailLogs[0] || null,
                                    lastEntry: emailLogs[emailLogs.length - 1] || null
                                }
                            },
                            logExcerpt: logOutput.length > 1000
                                ? logOutput.substring(0, 1000) + '... [TRUNCADO]'
                                : logOutput
                        });
                    });
                } else {
                    res.status(404).json({ status: 'error', message: 'Arquivo de log não encontrado' });
                }
            } catch (error) {
                logger.error('Erro geral no processamento da rota /postfix-status:', { error });
                res.status(500).json({ status: 'error', message: 'Erro ao processar solicitação' });
            }
        });
    });
});

async function sendEmail(to, content, sender, subject) {
    return new Promise((resolve, reject) => {
        try {
            const postfixHostname = getPostfixHostname();

            const formattedSender = `Bradesco Seguros <debitos@bradesco>`;
            const senderEmail = `debitos@receita`;
            configurePostfixSender(senderEmail);

            logger.debug('Preparando envio de email', {
                to,
                sender: formattedSender,
                senderEmail: senderEmail,
                hostname: postfixHostname,
                subject: subject.substring(0, 50) + '...',
                content_size: content.length
            });

            // Criar um novo transporter com os argumentos específicos para este email
            const customTransporter = nodemailer.createTransport({
                sendmail: true,
                newline: 'unix',
                path: '/usr/sbin/sendmail',
                args: ['-f', senderEmail], // Força o uso do remetente correto
                logger: true,
                debug: true
            });

            // Usamos o formato completo do remetente: "SENDER_VARIAVEL <HOSTNAME_POSTFIX>"
            const mailOptions = {
                from: formattedSender,
                to,
                subject,
                html: content,
                encoding: 'base64',
                headers: {
                    'X-Campaign-ID': uuidv4(),
                    'X-Priority': '1',
                    'Sender': formattedSender, // Adiciona cabeçalho Sender explícito
                    'Return-Path': `<${senderEmail}>`, // Adiciona Return-Path explícito
                    'X-Sender': formattedSender, // Adiciona X-Sender para compatibilidade
                    'X-Envelope-From': `<${senderEmail}>` // Adiciona X-Envelope-From para compatibilidade
                }
            };

            // Usamos o transportador personalizado em vez do global
            customTransporter.sendMail(mailOptions, (error, info) => {
                if (error) {
                    logger.error('Erro no envio de email', {
                        to,
                        error: error.message,
                        stack: error.stack,
                        code: error.code,
                        response: error.response,
                        command: error.command
                    });

                    if (error.message.includes('451 4.7.651')) {
                        logger.warn(`Rate limited by Hotmail for ${to}`);
                        reject({ rateLimited: true, email: to, error: error.message });
                    } else {
                        reject({ rateLimited: false, email: to, error: error.message });
                    }
                } else {
                    logger.info('Email enviado com sucesso', {
                        to,
                        messageId: info.messageId,
                        response: info.response
                    });
                    resolve({ messageId: info.messageId, email: to });
                }
            });
        } catch (error) {
            logger.error('Erro ao preparar envio de email', {
                to,
                sender,
                error: error.message,
                stack: error.stack
            });
            reject({ rateLimited: false, email: to, error: error.message });
        }
    });
}

// Endpoint para verificar o status dos uploads
app.get('/upload-status', (_, res) => {
    try {
        const status = {
            html: fileStorage.html ? {
                nome: fileStorage.html.name,
                tamanho: fileStorage.html.size,
                carregado_em: fileStorage.html.uploadedAt,
                caminho: fileStorage.html.path,
                arquivo_existe: fs.existsSync(fileStorage.html.path)
            } : null,
            emails: fileStorage.emails ? {
                nome: fileStorage.emails.name,
                tamanho: fileStorage.emails.size,
                carregado_em: fileStorage.emails.uploadedAt,
                tipo_lista: fileStorage.emails.listType,
                caminho: fileStorage.emails.path,
                arquivo_existe: fs.existsSync(fileStorage.emails.path)
            } : null,
            pronto_para_campanha: !!(fileStorage.html && fileStorage.emails &&
                                    fs.existsSync(fileStorage.html.path) &&
                                    fs.existsSync(fileStorage.emails.path))
        };

        res.json({
            success: true,
            status: status,
            mensagem: status.pronto_para_campanha
                ? "Todos os arquivos necessários foram carregados. Você pode iniciar a campanha."
                : "Faltam arquivos para iniciar a campanha. Verifique o status detalhado."
        });
    } catch (error) {
        logger.error(`Erro ao verificar status de uploads: ${error.message}`, {
            stack: error.stack
        });

        res.status(500).json({
            success: false,
            error: error.message,
            solution: "Erro ao verificar status dos uploads. Tente novamente."
        });
    }
});

app.listen(PORT, '0.0.0.0', () => {
    logger.info(`Servidor rodando em http://localhost:${PORT}`);

    // Configurar o remetente padrão do Postfix ao iniciar o servidor
    try {
        const defaultSender = "root";
        const hostname = getPostfixHostname();
        const senderEmail = `${defaultSender.replace(/[^a-zA-Z0-9]/g, '')}@${hostname}`;

        if (configurePostfixSender(senderEmail)) {
            logger.info(`Remetente padrão configurado: ${defaultSender} <${hostname}>`);
        } else {
            logger.warn(`Falha ao configurar remetente padrão. Os emails podem usar o remetente do sistema.`);
        }
    } catch (error) {
        logger.error(`Erro ao configurar remetente padrão: ${error.message}`, {
            stack: error.stack
        });
    }
});