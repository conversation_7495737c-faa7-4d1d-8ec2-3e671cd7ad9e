#!/usr/bin/env python3
import sqlite3
import asyncio
import asyncssh
import os
import sys  # Usado para sys.exit()
from colorama import Fore, init
import re
import tempfile

# Inicializar colorama
init(autoreset=True)

# Configurações
DB_PATH = "linodes.db"
DOCKER_CONTAINER_NAME = "api-enviador"
SSH_TIMEOUT = 10
SSH_PORT = 22
SSH_USERNAME = "root"  # Usuário SSH padrão
SSH_PASSWORD = "eE5522892004@@e"  # Senha SSH padrão para todas as VPS
MAX_CONCURRENT_CONNECTIONS = 5  # Número máximo de conexões simultâneas

def obter_ips_entregue():
    """Obtém os IPs das VMs marcadas como 'Entregue' na tabela serversAtivos."""
    ips_entregue = []
    try:
        with sqlite3.connect(DB_PATH) as conn:
            cursor = conn.cursor()

            # Consulta para obter IPs das máquinas marcadas como 'Entregue'
            cursor.execute("""
                SELECT ip
                FROM serversAtivos
                WHERE email_status = 'Entregue'
            """)

            for row in cursor.fetchall():
                ip = row[0]
                # Usamos a senha SSH global
                ips_entregue.append((ip, SSH_PASSWORD))

        print(Fore.CYAN + f"✅ {len(ips_entregue)} IPs marcados como 'Entregue' encontrados.")
        return ips_entregue
    except sqlite3.Error as e:
        print(Fore.RED + f"❌ Erro ao acessar o banco de dados: {e}")
        return []

async def conectar_ssh(ip, password):
    """Estabelece uma conexão SSH com a máquina remota usando asyncssh."""
    try:
        print(Fore.YELLOW + f"🔄 Conectando via SSH a {ip}...")
        conn = await asyncssh.connect(
            ip,
            port=SSH_PORT,
            username=SSH_USERNAME,
            password=password,
            known_hosts=None  # Ignora verificação de known_hosts
        )
        print(Fore.GREEN + f"✅ Conexão SSH estabelecida com {ip}")
        return conn
    except Exception as e:
        print(Fore.RED + f"❌ Falha ao conectar via SSH a {ip}: {e}")
        return None

async def verificar_container_docker(ssh_conn, container_name):
    """Verifica se o contêiner Docker existe e está em execução."""
    try:
        result = await ssh_conn.run(f"docker ps -a --filter name={container_name} --format '{{{{.Names}}}} {{{{.Status}}}}'")
        output = result.stdout.strip()

        if not output:
            print(Fore.RED + f"❌ Contêiner {container_name} não encontrado")
            return False, "não encontrado"

        if "Up" in output:
            print(Fore.GREEN + f"✅ Contêiner {container_name} está em execução")
            return True, "em execução"
        else:
            print(Fore.YELLOW + f"⚠️ Contêiner {container_name} existe mas não está em execução")
            return True, "parado"
    except Exception as e:
        print(Fore.RED + f"❌ Erro ao verificar contêiner Docker: {e}")
        return False, f"erro: {str(e)}"

async def encontrar_server_js(ssh_conn, ip):
    """Encontra o arquivo server.js dentro do contêiner Docker ou no sistema."""
    try:
        # Primeiro, verifica se o contêiner Docker existe
        container_existe, status = await verificar_container_docker(ssh_conn, DOCKER_CONTAINER_NAME)

        if container_existe and status == "em execução":
            # Tenta encontrar o arquivo dentro do contêiner
            result = await ssh_conn.run(f"docker exec {DOCKER_CONTAINER_NAME} find /app -name server.js")
            docker_paths = result.stdout.strip().split('\n')

            if docker_paths and docker_paths[0]:
                server_js_path = docker_paths[0]
                print(Fore.GREEN + f"✅ Arquivo server.js encontrado no contêiner: {server_js_path}")
                return True, server_js_path, "docker"

        # Se não encontrou no Docker ou o contêiner não está rodando, procura no sistema
        result = await ssh_conn.run("find / -name server.js -type f 2>/dev/null | grep -v 'node_modules'")
        system_paths = result.stdout.strip().split('\n')

        if system_paths and system_paths[0]:
            # Filtra caminhos mais prováveis (contendo 'enviador' ou 'app')
            filtered_paths = [p for p in system_paths if 'enviador' in p or '/app/' in p]

            if filtered_paths:
                server_js_path = filtered_paths[0]
            else:
                server_js_path = system_paths[0]

            print(Fore.GREEN + f"✅ Arquivo server.js encontrado no sistema: {server_js_path}")
            return True, server_js_path, "sistema"

        print(Fore.RED + f"❌ Não foi possível encontrar o arquivo server.js em {ip}")
        return False, "", ""
    except Exception as e:
        print(Fore.RED + f"❌ Erro ao procurar arquivo server.js: {e}")
        return False, "", ""

async def ler_arquivo_remoto(ssh_conn, caminho, tipo):
    """Lê o conteúdo de um arquivo remoto, seja no sistema ou no contêiner Docker."""
    try:
        if tipo == "docker":
            result = await ssh_conn.run(f"docker exec {DOCKER_CONTAINER_NAME} cat {caminho}")
        else:
            result = await ssh_conn.run(f"cat {caminho}")

        return True, result.stdout
    except Exception as e:
        print(Fore.RED + f"❌ Erro ao ler arquivo remoto: {e}")
        return False, ""

async def escrever_arquivo_remoto(ssh_conn, conteudo, caminho, tipo):
    """Escreve conteúdo em um arquivo remoto, seja no sistema ou no contêiner Docker."""
    try:
        # Cria um arquivo temporário local
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.js') as temp:
            temp_path = temp.name
            temp.write(conteudo)

        # Envia o arquivo para o servidor
        async with ssh_conn.start_sftp_client() as sftp:
            await sftp.put(temp_path, "/tmp/server.js.tmp")

        # Remove o arquivo temporário local
        os.unlink(temp_path)

        # Copia o arquivo para o destino final
        if tipo == "docker":
            await ssh_conn.run(f"docker cp /tmp/server.js.tmp {DOCKER_CONTAINER_NAME}:{caminho}")
        else:
            await ssh_conn.run(f"cp /tmp/server.js.tmp {caminho}")

        # Remove o arquivo temporário no servidor
        await ssh_conn.run("rm /tmp/server.js.tmp")

        return True
    except Exception as e:
        print(Fore.RED + f"❌ Erro ao escrever arquivo remoto: {e}")
        return False

async def reiniciar_servico(ssh_conn, ip, tipo):
    """Reinicia o serviço, seja o contêiner Docker ou outro serviço."""
    try:
        if tipo == "docker":
            print(Fore.YELLOW + f"🔄 Reiniciando contêiner Docker em {ip}...")
            await ssh_conn.run(f"docker restart {DOCKER_CONTAINER_NAME}")
        else:
            print(Fore.YELLOW + f"🔄 Reiniciando serviço em {ip}...")
            await ssh_conn.run("pm2 restart server.js || systemctl restart enviador || echo 'Nenhum serviço encontrado para reiniciar'")

        # Aguarda um pouco para o serviço iniciar
        await asyncio.sleep(5)

        print(Fore.GREEN + f"✅ Serviço reiniciado em {ip}")
        return True
    except Exception as e:
        print(Fore.RED + f"❌ Erro ao reiniciar serviço: {e}")
        return False

async def atualizar_sender(ssh_conn, ip):
    """Atualiza o sender no arquivo server.js de 'Receita Federal' para 'Bradesco Seguros'."""
    try:
        # Encontrar o arquivo server.js
        encontrado, caminho, tipo = await encontrar_server_js(ssh_conn, ip)

        if not encontrado:
            return False

        # Fazer backup do arquivo original
        print(Fore.YELLOW + f"🔄 Criando backup do arquivo {caminho}...")
        if tipo == "docker":
            await ssh_conn.run(f"docker exec {DOCKER_CONTAINER_NAME} cp {caminho} {caminho}.bak")
        else:
            await ssh_conn.run(f"cp {caminho} {caminho}.bak")

        print(Fore.GREEN + f"✅ Backup criado")

        # Ler o conteúdo atual do arquivo
        sucesso, conteudo = await ler_arquivo_remoto(ssh_conn, caminho, tipo)

        if not sucesso:
            return False

        # Verificar se o arquivo contém a string "Receita Federal"
        if "Receita Federal" not in conteudo:
            print(Fore.YELLOW + f"⚠️ A string 'Receita Federal' não foi encontrada no arquivo {caminho}")

        # Substituir todas as ocorrências de "Receita Federal" por "Bradesco Seguros"
        conteudo_modificado = conteudo.replace("Receita Federal", "Bradesco Seguros")

        # Modificar a linha específica na função sendEmail
        # Procurar por: const formattedSender = `...`;
        padrao = r'const formattedSender = `[^`]+`;'
        if re.search(padrao, conteudo):
            conteudo_modificado = re.sub(padrao, 'const formattedSender = `Bradesco Seguros <seguros@bradesco>`;', conteudo_modificado)
            print(Fore.GREEN + f"✅ Linha do formattedSender atualizada")
        else:
            print(Fore.YELLOW + f"⚠️ Não foi possível encontrar a linha do formattedSender")

        # Modificar a linha do senderEmail
        padrao_sender_email = r'const senderEmail = `[^`]+`;'
        if re.search(padrao_sender_email, conteudo):
            conteudo_modificado = re.sub(padrao_sender_email, 'const senderEmail = `seguros@bradesco`;', conteudo_modificado)
            print(Fore.GREEN + f"✅ Linha do senderEmail atualizada")
        else:
            print(Fore.YELLOW + f"⚠️ Não foi possível encontrar a linha do senderEmail")

        # Modificar as linhas que usam o hostname
        # Substituir qualquer código que use o hostname para formar o email
        padrao_hostname = r'const postfixHostname = getPostfixHostname\(\);'
        if re.search(padrao_hostname, conteudo):
            # Encontrar e substituir todo o bloco que usa postfixHostname
            conteudo_modificado = re.sub(
                r'const postfixHostname = getPostfixHostname\(\);(\s+).*?(\s+)const formattedSender',
                'const postfixHostname = "bradesco.com.br";\\1// Hostname fixo para o remetente\\2const formattedSender',
                conteudo_modificado,
                flags=re.DOTALL
            )
            print(Fore.GREEN + f"✅ Configuração de hostname atualizada")
        else:
            print(Fore.YELLOW + f"⚠️ Não foi possível encontrar a configuração de hostname")

        # Modificar a função configurePostfixSender para usar o domínio fixo
        padrao_configure_postfix = r'async function configurePostfixSender\(sender\) \{[\s\S]*?const ip = await getPublicIP\(\);[\s\S]*?const hostname = .*?;[\s\S]*?sender = .*?;'
        if re.search(padrao_configure_postfix, conteudo):
            conteudo_modificado = re.sub(
                padrao_configure_postfix,
                'async function configurePostfixSender(sender) {\n    try {\n        // Usar domínio fixo em vez de IP\n        const hostname = "bradesco.com.br";\n        sender = `seguros@${hostname}`;',
                conteudo_modificado
            )
            print(Fore.GREEN + f"✅ Função configurePostfixSender atualizada")
        else:
            print(Fore.YELLOW + f"⚠️ Não foi possível encontrar a função configurePostfixSender")

        # Modificar os cabeçalhos de email para usar o domínio fixo
        padrao_headers = r'headers: \{[^}]*\'Sender\': [^,]*,[^}]*\'Return-Path\': [^,]*,[^}]*\'X-Sender\': [^,]*,[^}]*\'X-Envelope-From\': [^}]*\}'
        if re.search(padrao_headers, conteudo):
            conteudo_modificado = re.sub(
                padrao_headers,
                'headers: {\n                    \'X-Campaign-ID\': uuidv4(),\n                    \'X-Priority\': \'1\',\n                    \'Sender\': formattedSender,\n                    \'Return-Path\': `<<EMAIL>>`,\n                    \'X-Sender\': formattedSender,\n                    \'X-Envelope-From\': `<<EMAIL>>`\n                }',
                conteudo_modificado
            )
            print(Fore.GREEN + f"✅ Cabeçalhos de email atualizados")
        else:
            print(Fore.YELLOW + f"⚠️ Não foi possível encontrar os cabeçalhos de email")

        # Escrever o conteúdo modificado no arquivo remoto
        if await escrever_arquivo_remoto(ssh_conn, conteudo_modificado, caminho, tipo):
            print(Fore.GREEN + f"✅ Arquivo {caminho} atualizado com sucesso em {ip}")
        else:
            return False

        # Reiniciar o serviço para aplicar as alterações
        if not await reiniciar_servico(ssh_conn, ip, tipo):
            return False

        return True

    except Exception as e:
        print(Fore.RED + f"❌ Erro ao atualizar o arquivo em {ip}: {e}")
        return False

async def processar_maquina(ip, password, semaforo):
    """Processa uma máquina, conectando via SSH e atualizando o sender."""
    async with semaforo:  # Limita o número de conexões simultâneas
        print(Fore.CYAN + f"\nProcessando máquina {ip}...")

        # Conectar via SSH
        ssh_conn = await conectar_ssh(ip, password)
        if not ssh_conn:
            return False

        try:
            # Atualizar o sender
            resultado = await atualizar_sender(ssh_conn, ip)
            return resultado
        except Exception as e:
            print(Fore.RED + f"❌ Erro ao processar máquina {ip}: {e}")
            return False
        finally:
            # Fechar conexão SSH
            ssh_conn.close()

async def main_async():
    print(Fore.CYAN + "=" * 80)
    print(Fore.CYAN + "SCRIPT DE ATUALIZAÇÃO DE SENDER NAS MÁQUINAS MARCADAS COMO 'ENTREGUE'")
    print(Fore.CYAN + "=" * 80)

    # Obter IPs das máquinas marcadas como "Entregue"
    ips_entregue = obter_ips_entregue()

    if not ips_entregue:
        print(Fore.RED + "❌ Nenhuma máquina marcada como 'Entregue' encontrada.")
        return

    # Criar semáforo para limitar conexões simultâneas
    semaforo = asyncio.Semaphore(MAX_CONCURRENT_CONNECTIONS)

    # Criar tarefas para processar cada máquina
    tarefas = []
    for ip, password in ips_entregue:
        tarefa = processar_maquina(ip, password, semaforo)
        tarefas.append(tarefa)

    # Executar todas as tarefas e aguardar os resultados
    resultados = await asyncio.gather(*tarefas, return_exceptions=True)

    # Contadores
    total = len(ips_entregue)
    sucesso = sum(1 for r in resultados if r is True)
    falha = total - sucesso

    # Resumo
    print(Fore.CYAN + "\n" + "=" * 80)
    print(Fore.CYAN + "RESUMO DA EXECUÇÃO")
    print(Fore.CYAN + "=" * 80)
    print(f"Total de máquinas processadas: {total}")
    print(Fore.GREEN + f"✅ Máquinas atualizadas com sucesso: {sucesso}")
    print(Fore.RED + f"❌ Máquinas com falha na atualização: {falha}")
    print(Fore.CYAN + "=" * 80)

def main():
    """Função principal que executa o loop de eventos assíncrono."""
    try:
        # Verificar se a senha SSH foi fornecida como argumento
        if len(sys.argv) > 1 and sys.argv[1] == "--password":
            if len(sys.argv) > 2:
                global SSH_PASSWORD
                SSH_PASSWORD = sys.argv[2]
                print(Fore.GREEN + f"✅ Senha SSH personalizada fornecida.")
            else:
                print(Fore.RED + "❌ Argumento --password fornecido, mas nenhuma senha especificada.")
                print(Fore.YELLOW + "Uso: ./update_sender.py --password SENHA")
                sys.exit(1)

        asyncio.run(main_async())
    except KeyboardInterrupt:
        print(Fore.YELLOW + "\n⚠️ Operação interrompida pelo usuário.")
    except Exception as e:
        print(Fore.RED + f"\n❌ Erro fatal: {e}")

if __name__ == "__main__":
    main()
