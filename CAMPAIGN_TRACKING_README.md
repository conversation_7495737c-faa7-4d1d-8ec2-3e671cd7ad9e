# Campaign Tracking System

This module adds campaign tracking functionality to the LinodeCreator system. It allows you to monitor active campaigns, view campaign history, and generate reports.

## Features

- **Campaign Monitoring**: Real-time monitoring of active campaigns with automatic updates every 60 seconds
- **Campaign History**: View detailed information about past campaigns
- **Campaign Reports**: Generate reports on campaign performance
- **CSV Export**: Export campaign data to CSV files for further analysis

## Installation

1. Make sure you have the required dependencies installed:

```bash
pip install -r requirements.txt
```

2. The campaign tracking system is automatically integrated with the main application. No additional setup is required.

## Usage

### Accessing the Campaign Tracking System

1. Launch the main application:

```bash
python main.py
```

2. From the main menu, select option `13. Monitorar campanhas`

### Campaign Tracking Menu Options

The campaign tracking system offers the following options:

1. **Monitor Active Campaigns**: Real-time monitoring of active campaigns with updates every 60 seconds
2. **View All Campaigns**: List all campaigns with filtering options (running, completed, all)
3. **View Campaign Details**: View detailed information about a specific campaign
4. **Generate Campaign Report**: Generate a report for a specific campaign or all campaigns
5. **Export All Campaigns to CSV**: Export all campaign data to a CSV file

### Campaign Data Stored

The system tracks the following information for each campaign:

- Campaign ID
- Campaign name
- Status (running, completed, failed)
- Start time and end time
- Server IP and label
- Total emails, sent emails, failed emails, pending emails
- Success rate and emails per minute

### Automatic Campaign Registration

Campaigns are automatically registered with the tracking system when:

1. A new campaign is started through the menu option `12. Iniciar campanha de email`
2. The system loads existing campaigns from the `campaign_ids.log` file

## Files

- `campaign_tracker.py`: Main implementation of the campaign tracking system
- `campaigns.db`: SQLite database storing campaign data
- `campaign_ids.log`: Log file with campaign IDs (used for initial loading)

## Troubleshooting

If you encounter any issues with the campaign tracking system:

1. Make sure the `tabulate` package is installed: `pip install tabulate`
2. Check that the `campaigns.db` file is writable
3. Verify that the `campaign_ids.log` file exists and is readable

## Technical Details

The campaign tracking system uses:

- SQLite for data storage
- Asyncio for non-blocking updates
- Tabulate for formatted table display
- CSV module for report exports

The system is designed to work without modifying the existing code, only adding new functionality.
