#!/usr/bin/env python3
import sqlite3
import asyncio
import asyncssh
import os
import sys
import re
import tempfile
from colorama import Fore, init

# Inicializar colorama
init(autoreset=True)

# Configurações
DB_PATH = "linodes.db"
DOCKER_CONTAINER_NAME = "api-enviador"
SSH_PORT = 22
SSH_USERNAME = "root"  # Usuário SSH padrão
SSH_PASSWORD = "eE5522892004@@e"  # Senha SSH padrão para todas as VPS
MAX_CONCURRENT_CONNECTIONS = 5  # Número máximo de conexões simultâneas

def obter_ips_entregue():
    """Obtém os IPs das VMs marcadas como 'Entregue' na tabela serversAtivos."""
    ips_entregue = []
    try:
        with sqlite3.connect(DB_PATH) as conn:
            cursor = conn.cursor()

            # Consulta para obter IPs das máquinas marcadas como 'Entregue'
            cursor.execute("""
                SELECT ip
                FROM serversAtivos
                WHERE email_status = 'Entregue'
            """)

            for row in cursor.fetchall():
                ip = row[0]
                # Usamos a senha SSH global
                ips_entregue.append((ip, SSH_PASSWORD))

        print(Fore.CYAN + f"✅ {len(ips_entregue)} IPs marcados como 'Entregue' encontrados.")
        return ips_entregue
    except sqlite3.Error as e:
        print(Fore.RED + f"❌ Erro ao acessar o banco de dados: {e}")
        return []

async def conectar_ssh(ip, password):
    """Estabelece uma conexão SSH com a máquina remota usando asyncssh."""
    try:
        print(Fore.YELLOW + f"🔄 Conectando via SSH a {ip}...")
        conn = await asyncssh.connect(
            ip,
            port=SSH_PORT,
            username=SSH_USERNAME,
            password=password,
            known_hosts=None  # Ignora verificação de known_hosts
        )
        print(Fore.GREEN + f"✅ Conexão SSH estabelecida com {ip}")
        return conn
    except Exception as e:
        print(Fore.RED + f"❌ Falha ao conectar via SSH a {ip}: {e}")
        return None

async def verificar_container_docker(ssh_conn, container_name):
    """Verifica se o contêiner Docker existe e está em execução."""
    try:
        result = await ssh_conn.run(f"docker ps -a --filter name={container_name} --format '{{{{.Names}}}} {{{{.Status}}}}'")
        output = result.stdout.strip()

        if not output:
            print(Fore.RED + f"❌ Contêiner {container_name} não encontrado")
            return False, "não encontrado"

        if "Up" in output:
            print(Fore.GREEN + f"✅ Contêiner {container_name} está em execução")
            return True, "em execução"
        else:
            print(Fore.YELLOW + f"⚠️ Contêiner {container_name} existe mas não está em execução")
            return True, "parado"
    except Exception as e:
        print(Fore.RED + f"❌ Erro ao verificar contêiner Docker: {e}")
        return False, f"erro: {str(e)}"

async def encontrar_server_js(ssh_conn, ip):
    """Encontra o arquivo server.js dentro do contêiner Docker ou no sistema."""
    try:
        # Primeiro, verifica se o contêiner Docker existe
        container_existe, status = await verificar_container_docker(ssh_conn, DOCKER_CONTAINER_NAME)

        if container_existe and status == "em execução":
            # Tenta encontrar o arquivo dentro do contêiner
            result = await ssh_conn.run(f"docker exec {DOCKER_CONTAINER_NAME} find /app -name server.js")
            docker_paths = result.stdout.strip().split('\n')

            if docker_paths and docker_paths[0]:
                server_js_path = docker_paths[0]
                print(Fore.GREEN + f"✅ Arquivo server.js encontrado no contêiner: {server_js_path}")
                return True, server_js_path, "docker"

        # Se não encontrou no Docker ou o contêiner não está rodando, procura no sistema
        result = await ssh_conn.run("find / -name server.js -type f 2>/dev/null | grep -v 'node_modules'")
        system_paths = result.stdout.strip().split('\n')

        if system_paths and system_paths[0]:
            # Filtra caminhos mais prováveis (contendo 'enviador' ou 'app')
            filtered_paths = [p for p in system_paths if 'enviador' in p or '/app/' in p]

            if filtered_paths:
                server_js_path = filtered_paths[0]
            else:
                server_js_path = system_paths[0]

            print(Fore.GREEN + f"✅ Arquivo server.js encontrado no sistema: {server_js_path}")
            return True, server_js_path, "sistema"

        print(Fore.RED + f"❌ Não foi possível encontrar o arquivo server.js em {ip}")
        return False, "", ""
    except Exception as e:
        print(Fore.RED + f"❌ Erro ao procurar arquivo server.js: {e}")
        return False, "", ""

async def corrigir_server_js(ssh_conn, ip):
    """Corrige os problemas de string de template no arquivo server.js para que o servidor possa iniciar corretamente."""
    try:
        # Encontrar o arquivo server.js
        encontrado, caminho, tipo = await encontrar_server_js(ssh_conn, ip)

        if not encontrado:
            return False

        # Fazer backup do arquivo original
        print(Fore.YELLOW + f"🔄 Criando backup do arquivo {caminho}...")
        if tipo == "docker":
            await ssh_conn.run(f"docker exec {DOCKER_CONTAINER_NAME} cp {caminho} {caminho}.bak.fix")
        else:
            await ssh_conn.run(f"cp {caminho} {caminho}.bak.fix")

        print(Fore.GREEN + f"✅ Backup criado em {caminho}.bak.fix")

        # Criar um script para corrigir todos os problemas de string de template
        print(Fore.YELLOW + "🔄 Criando script para corrigir todos os problemas de string de template...")
        fix_script = """#!/bin/bash
# Script para corrigir todos os problemas de string de template no arquivo server.js

# Caminho do arquivo
FILE="$1"

# Fazer backup do arquivo original
cp "$FILE" "$FILE.bak.script"

# Corrigir a linha 1470 (logger.info com string de template)
sed -i '1470s/logger.info(`Servidor rodando em http:\\/\\/localhost:${PORT}`);/logger.info("Servidor rodando em http:\\/\\/localhost:" + PORT);/' "$FILE"

# Corrigir a linha 1476 (senderEmail com string de template)
sed -i '1476s/const senderEmail = `adesao@bradesco`;/const senderEmail = "adesao@bradesco";/' "$FILE"

# Corrigir a linha 1159 (logger.error com string de template)
sed -i '1159s/logger.error("Erro na campanha ${req.params.id}: ${error.message}`);/logger.error("Erro na campanha " + req.params.id + ": " + error.message);/' "$FILE"

# Corrigir a linha 1399 (logger.warn com string de template)
sed -i '1399s/logger.warn(`Rate limited by Hotmail for ${to}`);/logger.warn("Rate limited by Hotmail for " + to);/' "$FILE"

# Corrigir a linha 1457 (logger.error com string de template)
sed -i '1457s/logger.error(`Erro ao verificar status de uploads: ${error.message}`, {/logger.error("Erro ao verificar status de uploads: " + error.message, {/' "$FILE"

# Corrigir a linha 1115 (logger.error com string de template)
sed -i '1115s/logger.error(`Campanha não encontrada: ${campaignId}`);/logger.error("Campanha não encontrada: " + campaignId);/' "$FILE"

echo "Correções aplicadas com sucesso!"
"""

        # Enviar o script para o servidor
        print(Fore.YELLOW + "🔄 Enviando script para o servidor...")
        await ssh_conn.run("echo '" + fix_script + "' > /tmp/fix_templates.sh")
        await ssh_conn.run("chmod +x /tmp/fix_templates.sh")

        # Executar o script no servidor
        print(Fore.YELLOW + "🔄 Executando script no servidor...")
        if tipo == "docker":
            # Copiar o script para o contêiner
            await ssh_conn.run(f"docker cp /tmp/fix_templates.sh {DOCKER_CONTAINER_NAME}:/tmp/fix_templates.sh")
            # Executar o script no contêiner
            result = await ssh_conn.run(f"docker exec {DOCKER_CONTAINER_NAME} bash /tmp/fix_templates.sh {caminho}")
        else:
            # Executar o script no sistema
            result = await ssh_conn.run(f"bash /tmp/fix_templates.sh {caminho}")

        print(Fore.CYAN + "=== Resultado da execução do script ===")
        print(result.stdout)

        # Reiniciar o serviço para aplicar as alterações
        print(Fore.YELLOW + f"🔄 Reiniciando o serviço em {ip}...")
        if tipo == "docker":
            await ssh_conn.run(f"docker restart {DOCKER_CONTAINER_NAME}")
        else:
            await ssh_conn.run("pm2 restart server.js || systemctl restart enviador || echo 'Nenhum serviço encontrado para reiniciar'")

        # Aguardar um pouco para o serviço iniciar
        await asyncio.sleep(5)

        print(Fore.GREEN + f"✅ Serviço reiniciado em {ip}")
        return True

    except Exception as e:
        print(Fore.RED + f"❌ Erro ao corrigir o arquivo em {ip}: {e}")
        return False

async def processar_maquina(ip, password, semaforo):
    """Processa uma máquina, conectando via SSH e corrigindo o arquivo server.js."""
    async with semaforo:  # Limita o número de conexões simultâneas
        print(Fore.CYAN + f"\nProcessando máquina {ip}...")

        # Conectar via SSH
        ssh_conn = await conectar_ssh(ip, password)
        if not ssh_conn:
            return False

        try:
            # Corrigir o arquivo server.js
            resultado = await corrigir_server_js(ssh_conn, ip)
            return resultado
        except Exception as e:
            print(Fore.RED + f"❌ Erro ao processar máquina {ip}: {e}")
            return False
        finally:
            # Fechar conexão SSH
            ssh_conn.close()

async def main_async():
    print(Fore.CYAN + "=" * 80)
    print(Fore.CYAN + "SCRIPT DE CORREÇÃO DO SERVER.JS NAS MÁQUINAS MARCADAS COMO 'ENTREGUE'")
    print(Fore.CYAN + "=" * 80)

    # Obter IPs das máquinas marcadas como "Entregue"
    ips_entregue = obter_ips_entregue()

    if not ips_entregue:
        print(Fore.RED + "❌ Nenhuma máquina marcada como 'Entregue' encontrada.")
        return

    # Criar semáforo para limitar conexões simultâneas
    semaforo = asyncio.Semaphore(MAX_CONCURRENT_CONNECTIONS)

    # Criar tarefas para processar cada máquina
    tarefas = []
    for ip, password in ips_entregue:
        tarefa = processar_maquina(ip, password, semaforo)
        tarefas.append(tarefa)

    # Executar todas as tarefas e aguardar os resultados
    resultados = await asyncio.gather(*tarefas, return_exceptions=True)

    # Contadores
    total = len(ips_entregue)
    sucesso = sum(1 for r in resultados if r is True)
    falha = total - sucesso

    # Resumo
    print(Fore.CYAN + "\n" + "=" * 80)
    print(Fore.CYAN + "RESUMO DA EXECUÇÃO")
    print(Fore.CYAN + "=" * 80)
    print(f"Total de máquinas processadas: {total}")
    print(Fore.GREEN + f"✅ Máquinas corrigidas com sucesso: {sucesso}")
    print(Fore.RED + f"❌ Máquinas com falha na correção: {falha}")
    print(Fore.CYAN + "=" * 80)

def main():
    """Função principal que executa o loop de eventos assíncrono."""
    try:
        asyncio.run(main_async())
    except KeyboardInterrupt:
        print(Fore.YELLOW + "\n⚠️ Operação interrompida pelo usuário.")
    except Exception as e:
        print(Fore.RED + f"\n❌ Erro fatal: {e}")

if __name__ == "__main__":
    main()
